﻿'use client'

import { useState, useEffect } from 'react'

export default function DiscoveryPage() {
  const [activeTab, setActiveTab] = useState('video')
  const [loading, setLoading] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState('all')

  // 交互状态
  const [likedItems, setLikedItems] = useState<Set<string>>(new Set())
  const [savedItems, setSavedItems] = useState<Set<string>>(new Set())
  const [followedUsers, setFollowedUsers] = useState<Set<string>>(new Set())
  const [joinedGroups, setJoinedGroups] = useState<Set<string>>(new Set(['c1']))

  // 弹窗状态
  const [showContentDetail, setShowContentDetail] = useState(false)
  const [showApplyProject, setShowApplyProject] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)
  const [showNFTModal, setShowNFTModal] = useState(false)
  const [showTipModal, setShowTipModal] = useState(false)
  const [showSuccessToast, setShowSuccessToast] = useState(false)
  const [toastMessage, setToastMessage] = useState('')

  // 选中的项目
  const [selectedContent, setSelectedContent] = useState<any>(null)
  const [selectedProject, setSelectedProject] = useState<any>(null)

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 筛选后的内容
  const [filteredContent, setFilteredContent] = useState<any>({})

  // 模拟发布的内容数据（对应发布页面的四大平台）
  const discoveryContent = {
    video: [
      {
        id: 'v1',
        title: 'NextGen 2025智慧城市建设项目展示',
        creator: '工程师·张三',
        avatar: '👨‍💻',
        thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',
        duration: '3:45',
        views: 12500,
        likes: 890,
        publishTime: '2小时前',
        tags: ['智慧城市', 'AI技术', '建筑设计'],
        description: '展示了最新的AI驱动智慧城市建设项目，包括智能交通系统和物联网基础设施...',
        nftEnabled: true,
        didVerified: true,
        rewards: 120
      },
      {
        id: 'v2',
        title: 'AI辅助建筑设计全流程演示',
        creator: '建筑师·王设计',
        avatar: '🏗️',
        thumbnail: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop',
        duration: '5:20',
        views: 8900,
        likes: 567,
        publishTime: '4小时前',
        tags: ['建筑设计', 'AI辅助', 'BIM'],
        description: '完整展示AI辅助建筑设计的全流程，从概念设计到施工图生成...',
        nftEnabled: false,
        didVerified: true,
        rewards: 85
      },
      {
        id: 'v3',
        title: '元宇宙虚拟展厅设计案例',
        creator: 'VR设计师·小李',
        avatar: '🌌',
        thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',
        duration: '4:15',
        views: 15600,
        likes: 1234,
        publishTime: '6小时前',
        tags: ['元宇宙', 'VR设计', '虚拟展厅'],
        description: '创新的元宇宙虚拟展厅设计，支持多人实时交互和3D展示...',
        nftEnabled: true,
        didVerified: true,
        rewards: 150
      }
    ],
    discovery: [
      {
        id: 'd1',
        title: '2025年建筑行业AI应用趋势报告',
        creator: '行业分析师·陈专家',
        avatar: '📊',
        images: [
          'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
          'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop'
        ],
        readTime: '8分钟',
        views: 5600,
        likes: 234,
        publishTime: '1小时前',
        tags: ['行业报告', 'AI应用', '建筑趋势'],
        description: '深度分析2025年建筑行业AI应用的最新趋势，包括设计自动化、施工机器人等...',
        nftEnabled: false,
        didVerified: true,
        rewards: 75
      },
      {
        id: 'd2',
        title: '智能建筑物联网系统设计指南',
        creator: '物联网工程师·刘技术',
        avatar: '🔗',
        images: [
          'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop'
        ],
        readTime: '12分钟',
        views: 3400,
        likes: 189,
        publishTime: '3小时前',
        tags: ['物联网', '智能建筑', '系统设计'],
        description: '详细介绍智能建筑物联网系统的设计原理、技术架构和实施方案...',
        nftEnabled: true,
        didVerified: true,
        rewards: 95
      }
    ],
    community: [
      {
        id: 'c1',
        title: '建筑师AI工具使用经验分享',
        creator: '建筑师联盟',
        avatar: '🏗️',
        groupType: '技术讨论',
        members: 2340,
        posts: 156,
        publishTime: '30分钟前',
        tags: ['经验分享', 'AI工具', '建筑师'],
        description: '分享各种AI工具在建筑设计中的实际应用经验，包括Midjourney、Stable Diffusion等...',
        isJoined: true,
        activity: 'high'
      },
      {
        id: 'c2',
        title: 'Web3建设者技术讨论群',
        creator: 'Web3建设者',
        avatar: '🌐',
        groupType: '技术交流',
        members: 1567,
        posts: 89,
        publishTime: '1小时前',
        tags: ['Web3', '区块链', '技术讨论'],
        description: '讨论Web3技术在建筑和工程领域的应用，包括DeFi、NFT、DAO等...',
        isJoined: false,
        activity: 'medium'
      }
    ],
    ecosystem: [
      {
        id: 'e1',
        title: '寻求AI建筑设计合作伙伴',
        creator: '建筑事务所·王总',
        avatar: '🏢',
        budget: '50-100万',
        duration: '3-6个月',
        location: '北京',
        publishTime: '2小时前',
        tags: ['项目合作', 'AI建筑', '设计服务'],
        description: '我们正在开发一个大型商业综合体项目，需要AI建筑设计方面的合作伙伴...',
        requirements: ['AI设计经验', 'BIM技术', '团队规模10+'],
        matchType: '技术合作',
        status: 'open'
      },
      {
        id: 'e2',
        title: '智慧城市项目寻求技术团队',
        creator: '政府采购部门',
        avatar: '🏛️',
        budget: '200-500万',
        duration: '6-12个月',
        location: '上海',
        publishTime: '4小时前',
        tags: ['政府项目', '智慧城市', '技术团队'],
        description: '智慧城市基础设施建设项目，需要具备AI、物联网、大数据技术的团队...',
        requirements: ['政府项目经验', '资质齐全', '技术实力强'],
        matchType: '工程项目匹配',
        status: 'open'
      }
    ]
  }

  // 分类选项
  const categories = [
    { key: 'all', name: '全部', icon: '🌟' },
    { key: 'ai', name: 'AI技术', icon: '🤖' },
    { key: 'architecture', name: '建筑设计', icon: '🏗️' },
    { key: 'smart-city', name: '智慧城市', icon: '🏙️' },
    { key: 'web3', name: 'Web3', icon: '🌐' },
    { key: 'iot', name: '物联网', icon: '🔗' },
    { key: 'vr', name: 'VR/AR', icon: '🥽' }
  ]

  // 显示成功提示
  const showToast = (message: string) => {
    setToastMessage(message)
    setShowSuccessToast(true)
    setTimeout(() => setShowSuccessToast(false), 3000)
  }

  // 搜索和筛选功能
  useEffect(() => {
    const filterContent = () => {
      const filtered: any = {}

      Object.keys(discoveryContent).forEach(type => {
        filtered[type] = discoveryContent[type as keyof typeof discoveryContent].filter((item: any) => {
          // 分类筛选
          const matchesCategory = selectedCategory === 'all' ||
            item.tags.some((tag: string) => {
              switch(selectedCategory) {
                case 'ai': return tag.includes('AI') || tag.includes('智能')
                case 'architecture': return tag.includes('建筑') || tag.includes('设计')
                case 'smart-city': return tag.includes('智慧城市') || tag.includes('城市')
                case 'web3': return tag.includes('Web3') || tag.includes('区块链') || tag.includes('NFT')
                case 'iot': return tag.includes('物联网') || tag.includes('IoT')
                case 'vr': return tag.includes('VR') || tag.includes('AR') || tag.includes('元宇宙')
                default: return true
              }
            })

          return matchesCategory
        })
      })

      setFilteredContent(filtered)
    }

    filterContent()
  }, [selectedCategory])

  // 点赞功能
  const handleLike = (itemId: string) => {
    setLikedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
        showToast('取消点赞')
      } else {
        newSet.add(itemId)
        showToast('点赞成功')
      }
      return newSet
    })
  }

  // 收藏功能
  const handleSave = (itemId: string) => {
    setSavedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
        showToast('取消收藏')
      } else {
        newSet.add(itemId)
        showToast('收藏成功')
      }
      return newSet
    })
  }

  // 关注功能
  const handleFollow = (userId: string) => {
    setFollowedUsers(prev => {
      const newSet = new Set(prev)
      if (newSet.has(userId)) {
        newSet.delete(userId)
        showToast('取消关注')
      } else {
        newSet.add(userId)
        showToast('关注成功')
      }
      return newSet
    })
  }

  // 观看视频
  const handleWatchVideo = (video: any) => {
    setSelectedContent(video)
    setShowContentDetail(true)
  }

  // 阅读文章
  const handleReadArticle = (article: any) => {
    setSelectedContent(article)
    setShowContentDetail(true)
  }

  // 分享功能
  const handleShare = (content: any) => {
    setSelectedContent(content)
    setShowShareModal(true)
  }

  // 加入/退出群组
  const handleJoinGroup = (groupId: string) => {
    setJoinedGroups(prev => {
      const newSet = new Set(prev)
      if (newSet.has(groupId)) {
        newSet.delete(groupId)
        showToast('已退出群组')
      } else {
        newSet.add(groupId)
        showToast('成功加入群组')
      }
      return newSet
    })
  }

  // 查看项目详情
  const handleViewProject = (project: any) => {
    setSelectedProject(project)
    setShowContentDetail(true)
  }

  // 申请项目
  const handleApplyProject = (project: any) => {
    setSelectedProject(project)
    setShowApplyProject(true)
  }

  // NFT购买
  const handleBuyNFT = (content: any) => {
    setSelectedContent(content)
    setShowNFTModal(true)
  }

  // 创作者打赏
  const handleTipCreator = (content: any) => {
    setSelectedContent(content)
    setShowTipModal(true)
  }

  return (
    <div className="min-h-screen text-white">
      <div className="relative z-10">
        {/* 顶部导航 */}
        <div className="sticky top-0 border-b border-white/10 p-4">







        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 p-4 pb-20">
          {/* 视频内容 */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-white mb-4">🎬 视频内容</h2>
            <div className="space-y-4">
              {(filteredContent.video || discoveryContent.video).map((video) => (
                <div key={video.id} className="rounded-xl overflow-hidden border border-white/20">
                  <div className="relative">
                    <img
                      src={video.thumbnail}
                      alt={video.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute bottom-2 right-2 bg-black/70 px-2 py-1 rounded text-xs text-white">
                      {video.duration}
                    </div>
                    <div className="absolute top-2 left-2 flex space-x-2">
                      {video.nftEnabled && (
                        <span className="bg-purple-500 px-2 py-1 rounded-full text-xs font-medium">
                          🎨 NFT
                        </span>
                      )}
                      {video.didVerified && (
                        <span className="bg-blue-500 px-2 py-1 rounded-full text-xs font-medium">
                          🆔 DID
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="p-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center">
                        {video.avatar}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-white">{video.title}</h3>
                        <p className="text-sm text-gray-400">{video.creator} • {video.publishTime}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-green-400 font-bold text-sm">+{video.rewards} NGT</div>
                        <div className="text-xs text-gray-400">奖励</div>
                      </div>
                    </div>

                    <p className="text-gray-300 text-sm mb-3 line-clamp-2">{video.description}</p>

                    <div className="flex flex-wrap gap-2 mb-3">
                      {video.tags.map((tag, idx) => (
                        <span key={idx} className="px-2 py-1 border border-purple-300/30 rounded text-xs text-purple-300">
                          #{tag}
                        </span>
                      ))}
                    </div>

                    {/* 统一交互按钮设计 */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-6">
                        <button
                          onClick={() => handleLike(video.id)}
                          className={`flex flex-col items-center space-y-1 transition-colors ${
                            likedItems.has(video.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'
                          }`}
                        >
                          <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                            👍
                          </div>
                          <span className="text-xs">{video.likes + (likedItems.has(video.id) ? 1 : 0)}</span>
                        </button>

                        <button
                          onClick={() => handleShare(video)}
                          className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors"
                        >
                          <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                            🔄
                          </div>
                          <span className="text-xs">33</span>
                        </button>

                        <button
                          onClick={() => handleSave(video.id)}
                          className={`flex flex-col items-center space-y-1 transition-colors ${
                            savedItems.has(video.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'
                          }`}
                        >
                          <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                            ❤️
                          </div>
                          <span className="text-xs">45</span>
                        </button>

                        <button className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors">
                          <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                            💬
                          </div>
                          <span className="text-xs">67</span>
                        </button>
                      </div>

                      <div className="flex space-x-2">
                        {video.nftEnabled && (
                          <button
                            onClick={() => handleBuyNFT(video)}
                            className="px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors"
                          >
                            购买NFT
                          </button>
                        )}
                        <button
                          onClick={() => handleWatchVideo(video)}
                          className="px-3 py-1 bg-purple-500 rounded-lg text-xs font-medium hover:bg-purple-600 transition-colors"
                        >
                          观看
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 图文发现 */}
          {activeTab === 'discovery' && (
            <div className="space-y-4">
              {(filteredContent.discovery || discoveryContent.discovery).map((article: any) => (
                <div key={article.id} className="rounded-xl overflow-hidden border border-white/20">
                  <div className="flex">
                    <div className="flex-1 p-4">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-sm">
                          {article.avatar}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-gray-400">{article.creator} • {article.publishTime}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-green-400 font-bold text-sm">+{article.rewards} NGT</div>
                        </div>
                      </div>

                      <h3 className="font-bold text-white mb-2">{article.title}</h3>
                      <p className="text-gray-300 text-sm mb-3 line-clamp-3">{article.description}</p>

                      <div className="flex flex-wrap gap-2 mb-3">
                        {article.tags.map((tag, idx) => (
                          <span key={idx} className="px-2 py-1 border border-blue-300/30 rounded text-xs text-blue-300">
                            #{tag}
                          </span>
                        ))}
                      </div>

                      {/* 统一交互按钮设计 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-6">
                          <button
                            onClick={() => handleLike(article.id)}
                            className={`flex flex-col items-center space-y-1 transition-colors ${
                              likedItems.has(article.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'
                            }`}
                          >
                            <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                              👍
                            </div>
                            <span className="text-xs">{article.likes + (likedItems.has(article.id) ? 1 : 0)}</span>
                          </button>

                          <button
                            onClick={() => handleShare(article)}
                            className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors"
                          >
                            <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                              🔄
                            </div>
                            <span className="text-xs">33</span>
                          </button>

                          <button
                            onClick={() => handleSave(article.id)}
                            className={`flex flex-col items-center space-y-1 transition-colors ${
                              savedItems.has(article.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'
                            }`}
                          >
                            <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                              ❤️
                            </div>
                            <span className="text-xs">45</span>
                          </button>

                          <button className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors">
                            <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                              💬
                            </div>
                            <span className="text-xs">67</span>
                          </button>
                        </div>

                        <div className="flex space-x-2">
                          {article.nftEnabled && (
                            <button
                              onClick={() => handleBuyNFT(article)}
                              className="px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors"
                            >
                              购买NFT
                            </button>
                          )}
                          <button
                            onClick={() => handleReadArticle(article)}
                            className="px-3 py-1 bg-blue-500 rounded-lg text-xs font-medium hover:bg-blue-600 transition-colors"
                          >
                            阅读
                          </button>
                        </div>
                      </div>
                    </div>

                    {article.images && article.images.length > 0 && (
                      <div className="w-24 h-24 m-4 rounded-lg overflow-hidden">
                        <img
                          src={article.images[0]}
                          alt={article.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 社群讨论 */}
          {activeTab === 'community' && (
            <div className="space-y-4">
              {(filteredContent.community || discoveryContent.community).map((community: any) => (
                <div key={community.id} className="rounded-xl p-4 border border-white/20">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl">
                        {community.avatar}
                      </div>
                      <div>
                        <h3 className="font-bold text-white">{community.title}</h3>
                        <p className="text-sm text-gray-400">{community.creator} • {community.publishTime}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        community.activity === 'high' ? 'bg-green-400' :
                        community.activity === 'medium' ? 'bg-yellow-400' : 'bg-gray-400'
                      }`}></div>
                      <span className="text-xs text-gray-400">
                        {community.activity === 'high' ? '活跃' :
                         community.activity === 'medium' ? '一般' : '较少'}
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-300 text-sm mb-3">{community.description}</p>

                  <div className="flex flex-wrap gap-2 mb-3">
                    {community.tags.map((tag, idx) => (
                      <span key={idx} className="px-2 py-1 border border-green-300/30 rounded text-xs text-green-300">
                        #{tag}
                      </span>
                    ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <span>👥 {community.members.toLocaleString()} 成员</span>
                      <span>💬 {community.posts} 讨论</span>
                      <span className="px-2 py-1 border border-blue-300/30 rounded text-xs text-blue-300">
                        {community.groupType}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleShare(community)}
                        className="px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors"
                      >
                        分享
                      </button>
                      <button
                        onClick={() => handleJoinGroup(community.id)}
                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                          joinedGroups.has(community.id)
                            ? 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                            : 'bg-gradient-to-r from-green-500 to-teal-500 text-white hover:from-green-600 hover:to-teal-600'
                        }`}
                      >
                        {joinedGroups.has(community.id) ? '已加入' : '加入讨论'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 生态匹配 */}
          {activeTab === 'ecosystem' && (
            <div className="space-y-4">
              {(filteredContent.ecosystem || discoveryContent.ecosystem).map((project: any) => (
                <div key={project.id} className="rounded-xl p-4 border border-white/20">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center text-xl">
                        {project.avatar}
                      </div>
                      <div>
                        <h3 className="font-bold text-white">{project.title}</h3>
                        <p className="text-sm text-gray-400">{project.creator} • {project.publishTime}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                        project.status === 'open' ? 'bg-green-500' : 'bg-gray-500'
                      }`}>
                        {project.status === 'open' ? '招募中' : '已结束'}
                      </div>
                    </div>
                  </div>

                  <p className="text-gray-300 text-sm mb-3">{project.description}</p>

                  <div className="grid grid-cols-2 gap-4 mb-3">
                    <div>
                      <div className="text-xs text-gray-400">预算范围</div>
                      <div className="text-green-400 font-bold">{project.budget}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-400">项目周期</div>
                      <div className="text-blue-400 font-bold">{project.duration}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-400">项目地点</div>
                      <div className="text-purple-400 font-bold">{project.location}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-400">匹配类型</div>
                      <div className="text-orange-400 font-bold">{project.matchType}</div>
                    </div>
                  </div>

                  <div className="mb-3">
                    <div className="text-xs text-gray-400 mb-2">技能要求</div>
                    <div className="flex flex-wrap gap-2">
                      {project.requirements.map((req, idx) => (
                        <span key={idx} className="px-2 py-1 border border-orange-300/30 rounded text-xs text-orange-300">
                          {req}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-3">
                    {project.tags.map((tag, idx) => (
                      <span key={idx} className="px-2 py-1 border border-red-300/30 rounded text-xs text-red-300">
                        #{tag}
                      </span>
                    ))}
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={() => handleShare(project)}
                      className="px-3 py-2 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors"
                    >
                      分享
                    </button>
                    <button
                      onClick={() => handleSave(project.id)}
                      className={`px-3 py-2 rounded-lg text-xs font-medium transition-colors ${
                        savedItems.has(project.id)
                          ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500'
                          : 'bg-white/10 text-gray-300 hover:bg-white/20'
                      }`}
                    >
                      {savedItems.has(project.id) ? '已收藏' : '收藏'}
                    </button>
                    <button
                      onClick={() => handleViewProject(project)}
                      className="flex-1 py-2 bg-white/10 border border-white/20 rounded-lg text-sm font-medium hover:bg-white/20 transition-colors"
                    >
                      查看详情
                    </button>
                    <button
                      onClick={() => handleApplyProject(project)}
                      className="flex-1 py-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-colors"
                    >
                      立即申请
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 成功提示 */}
      {showSuccessToast && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <span>✅</span>
            <span>{toastMessage}</span>
          </div>
        </div>
      )}

      {/* 内容详情弹窗 */}
      {showContentDetail && selectedContent && (
        <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">内容详情</h3>
              <button
                onClick={() => setShowContentDetail(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-xl">
                  {selectedContent.avatar}
                </div>
                <div>
                  <h4 className="font-bold text-white">{selectedContent.title}</h4>
                  <p className="text-sm text-gray-400">{selectedContent.creator}</p>
                </div>
              </div>

              <p className="text-gray-300 text-sm">{selectedContent.description}</p>

              {selectedContent.duration && (
                <div className="text-sm text-gray-400">
                  时长: {selectedContent.duration}
                </div>
              )}

              {selectedContent.readTime && (
                <div className="text-sm text-gray-400">
                  阅读时间: {selectedContent.readTime}
                </div>
              )}

              <div className="flex flex-wrap gap-2">
                {selectedContent.tags?.map((tag: string, idx: number) => (
                  <span key={idx} className="px-2 py-1 border border-purple-300/30 rounded text-xs text-purple-300">
                    #{tag}
                  </span>
                ))}
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={() => {
                    setShowContentDetail(false)
                    handleTipCreator(selectedContent)
                  }}
                  className="flex-1 py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl text-white font-medium hover:from-green-600 hover:to-teal-600 transition-colors"
                >
                  💰 打赏创作者
                </button>
                <button
                  onClick={() => setShowContentDetail(false)}
                  className="flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 分享弹窗 */}
      {showShareModal && selectedContent && (
        <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">分享内容</h3>
              <button
                onClick={() => setShowShareModal(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>

            <div className="grid grid-cols-3 gap-4 mb-6">
              {[
                { name: '微信', icon: '💬', color: 'bg-green-500' },
                { name: '微博', icon: '📱', color: 'bg-red-500' },
                { name: 'QQ', icon: '🐧', color: 'bg-blue-500' },
                { name: '钉钉', icon: '💼', color: 'bg-blue-600' },
                { name: '复制链接', icon: '🔗', color: 'bg-gray-500' },
                { name: '更多', icon: '⋯', color: 'bg-purple-500' }
              ].map((platform) => (
                <button
                  key={platform.name}
                  onClick={() => {
                    showToast(`已分享到${platform.name}`)
                    setShowShareModal(false)
                  }}
                  className={`${platform.color} rounded-xl p-4 text-white text-center hover:opacity-80 transition-opacity`}
                >
                  <div className="text-2xl mb-1">{platform.icon}</div>
                  <div className="text-xs">{platform.name}</div>
                </button>
              ))}
            </div>

            <button
              onClick={() => setShowShareModal(false)}
              className="w-full py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors"
            >
              取消
            </button>
          </div>
        </div>
      )}

      {/* 项目申请弹窗 */}
      {showApplyProject && selectedProject && (
        <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">申请项目</h3>
              <button
                onClick={() => setShowApplyProject(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="font-bold text-white mb-2">{selectedProject.title}</h4>
                <p className="text-gray-400 text-sm">{selectedProject.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-xs text-gray-400">预算范围</div>
                  <div className="text-green-400 font-bold">{selectedProject.budget}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-400">项目周期</div>
                  <div className="text-blue-400 font-bold">{selectedProject.duration}</div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">个人简介</label>
                <textarea
                  className="w-full border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                  rows={3}
                  placeholder="请简要介绍您的相关经验和技能..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">联系方式</label>
                <input
                  type="text"
                  className="w-full border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                  placeholder="请输入您的联系方式..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">期望报酬</label>
                <input
                  type="text"
                  className="w-full border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                  placeholder="请输入您的期望报酬..."
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={() => setShowApplyProject(false)}
                  className="flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={() => {
                    setShowApplyProject(false)
                    showToast('申请已提交，请等待回复')
                  }}
                  className="flex-1 py-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl text-white font-medium hover:from-orange-600 hover:to-red-600 transition-colors"
                >
                  提交申请
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* NFT购买弹窗 */}
      {showNFTModal && selectedContent && (
        <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">购买NFT</h3>
              <button
                onClick={() => setShowNFTModal(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-3xl mx-auto mb-3">
                  🎨
                </div>
                <h4 className="font-bold text-white">{selectedContent.title}</h4>
                <p className="text-gray-400 text-sm">by {selectedContent.creator}</p>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-400">NFT价格</span>
                  <span className="text-green-400 font-bold">0.1 ETH</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-400">Gas费用</span>
                  <span className="text-blue-400">0.005 ETH</span>
                </div>
                <div className="border-t border-white/10 pt-2">
                  <div className="flex justify-between items-center">
                    <span className="text-white font-bold">总计</span>
                    <span className="text-green-400 font-bold">0.105 ETH</span>
                  </div>
                </div>
              </div>

              <div className="text-xs text-gray-400">
                购买后您将获得该内容的NFT所有权证明，可在二级市场交易。
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowNFTModal(false)}
                  className="flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={() => {
                    setShowNFTModal(false)
                    showToast('NFT购买成功！')
                  }}
                  className="flex-1 py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl text-white font-medium hover:from-purple-600 hover:to-pink-600 transition-colors"
                >
                  确认购买
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 打赏弹窗 */}
      {showTipModal && selectedContent && (
        <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">打赏创作者</h3>
              <button
                onClick={() => setShowTipModal(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-2xl mx-auto mb-2">
                  {selectedContent.avatar}
                </div>
                <p className="text-white font-medium">{selectedContent.creator}</p>
                <p className="text-gray-400 text-sm">感谢优质内容创作</p>
              </div>

              <div className="grid grid-cols-3 gap-3">
                {[10, 50, 100, 200, 500, 1000].map((amount) => (
                  <button
                    key={amount}
                    onClick={() => {
                      setShowTipModal(false)
                      showToast(`成功打赏 ${amount} NGT`)
                    }}
                    className="py-3 bg-white/10 border border-white/20 rounded-lg text-white font-medium hover:bg-white/20 transition-colors"
                  >
                    {amount} NGT
                  </button>
                ))}
              </div>

              <div>
                <input
                  type="number"
                  placeholder="自定义金额"
                  className="w-full border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-green-500"
                />
              </div>

              <button
                onClick={() => setShowTipModal(false)}
                className="w-full py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
