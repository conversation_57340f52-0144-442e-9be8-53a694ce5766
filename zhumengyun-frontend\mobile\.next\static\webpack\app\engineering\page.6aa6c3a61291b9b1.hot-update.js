"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/engineering/page",{

/***/ "(app-pages-browser)/./src/app/engineering/page.tsx":
/*!**************************************!*\
  !*** ./src/app/engineering/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DiscoveryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DiscoveryPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('video');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // 交互状态\n    const [likedItems, setLikedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [followedUsers, setFollowedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [joinedGroups, setJoinedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'c1'\n    ]));\n    // 弹窗状态\n    const [showContentDetail, setShowContentDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showApplyProject, setShowApplyProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNFTModal, setShowNFTModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTipModal, setShowTipModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccessToast, setShowSuccessToast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toastMessage, setToastMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 选中的项目\n    const [selectedContent, setSelectedContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 加载状态\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 筛选后的内容\n    const [filteredContent, setFilteredContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 模拟发布的内容数据（对应发布页面的四大平台）\n    const discoveryContent = {\n        video: [\n            {\n                id: 'v1',\n                title: 'NextGen 2025智慧城市建设项目展示',\n                creator: '工程师·张三',\n                avatar: '👨‍💻',\n                thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',\n                duration: '3:45',\n                views: 12500,\n                likes: 890,\n                publishTime: '2小时前',\n                tags: [\n                    '智慧城市',\n                    'AI技术',\n                    '建筑设计'\n                ],\n                description: '展示了最新的AI驱动智慧城市建设项目，包括智能交通系统和物联网基础设施...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 120\n            },\n            {\n                id: 'v2',\n                title: 'AI辅助建筑设计全流程演示',\n                creator: '建筑师·王设计',\n                avatar: '🏗️',\n                thumbnail: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop',\n                duration: '5:20',\n                views: 8900,\n                likes: 567,\n                publishTime: '4小时前',\n                tags: [\n                    '建筑设计',\n                    'AI辅助',\n                    'BIM'\n                ],\n                description: '完整展示AI辅助建筑设计的全流程，从概念设计到施工图生成...',\n                nftEnabled: false,\n                didVerified: true,\n                rewards: 85\n            },\n            {\n                id: 'v3',\n                title: '元宇宙虚拟展厅设计案例',\n                creator: 'VR设计师·小李',\n                avatar: '🌌',\n                thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',\n                duration: '4:15',\n                views: 15600,\n                likes: 1234,\n                publishTime: '6小时前',\n                tags: [\n                    '元宇宙',\n                    'VR设计',\n                    '虚拟展厅'\n                ],\n                description: '创新的元宇宙虚拟展厅设计，支持多人实时交互和3D展示...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 150\n            }\n        ],\n        discovery: [\n            {\n                id: 'd1',\n                title: '2025年建筑行业AI应用趋势报告',\n                creator: '行业分析师·陈专家',\n                avatar: '📊',\n                images: [\n                    'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',\n                    'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop'\n                ],\n                readTime: '8分钟',\n                views: 5600,\n                likes: 234,\n                publishTime: '1小时前',\n                tags: [\n                    '行业报告',\n                    'AI应用',\n                    '建筑趋势'\n                ],\n                description: '深度分析2025年建筑行业AI应用的最新趋势，包括设计自动化、施工机器人等...',\n                nftEnabled: false,\n                didVerified: true,\n                rewards: 75\n            },\n            {\n                id: 'd2',\n                title: '智能建筑物联网系统设计指南',\n                creator: '物联网工程师·刘技术',\n                avatar: '🔗',\n                images: [\n                    'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop'\n                ],\n                readTime: '12分钟',\n                views: 3400,\n                likes: 189,\n                publishTime: '3小时前',\n                tags: [\n                    '物联网',\n                    '智能建筑',\n                    '系统设计'\n                ],\n                description: '详细介绍智能建筑物联网系统的设计原理、技术架构和实施方案...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 95\n            }\n        ],\n        community: [\n            {\n                id: 'c1',\n                title: '建筑师AI工具使用经验分享',\n                creator: '建筑师联盟',\n                avatar: '🏗️',\n                groupType: '技术讨论',\n                members: 2340,\n                posts: 156,\n                publishTime: '30分钟前',\n                tags: [\n                    '经验分享',\n                    'AI工具',\n                    '建筑师'\n                ],\n                description: '分享各种AI工具在建筑设计中的实际应用经验，包括Midjourney、Stable Diffusion等...',\n                isJoined: true,\n                activity: 'high'\n            },\n            {\n                id: 'c2',\n                title: 'Web3建设者技术讨论群',\n                creator: 'Web3建设者',\n                avatar: '🌐',\n                groupType: '技术交流',\n                members: 1567,\n                posts: 89,\n                publishTime: '1小时前',\n                tags: [\n                    'Web3',\n                    '区块链',\n                    '技术讨论'\n                ],\n                description: '讨论Web3技术在建筑和工程领域的应用，包括DeFi、NFT、DAO等...',\n                isJoined: false,\n                activity: 'medium'\n            }\n        ],\n        ecosystem: [\n            {\n                id: 'e1',\n                title: '寻求AI建筑设计合作伙伴',\n                creator: '建筑事务所·王总',\n                avatar: '🏢',\n                budget: '50-100万',\n                duration: '3-6个月',\n                location: '北京',\n                publishTime: '2小时前',\n                tags: [\n                    '项目合作',\n                    'AI建筑',\n                    '设计服务'\n                ],\n                description: '我们正在开发一个大型商业综合体项目，需要AI建筑设计方面的合作伙伴...',\n                requirements: [\n                    'AI设计经验',\n                    'BIM技术',\n                    '团队规模10+'\n                ],\n                matchType: '技术合作',\n                status: 'open'\n            },\n            {\n                id: 'e2',\n                title: '智慧城市项目寻求技术团队',\n                creator: '政府采购部门',\n                avatar: '🏛️',\n                budget: '200-500万',\n                duration: '6-12个月',\n                location: '上海',\n                publishTime: '4小时前',\n                tags: [\n                    '政府项目',\n                    '智慧城市',\n                    '技术团队'\n                ],\n                description: '智慧城市基础设施建设项目，需要具备AI、物联网、大数据技术的团队...',\n                requirements: [\n                    '政府项目经验',\n                    '资质齐全',\n                    '技术实力强'\n                ],\n                matchType: '工程项目匹配',\n                status: 'open'\n            }\n        ]\n    };\n    // 分类选项\n    const categories = [\n        {\n            key: 'all',\n            name: '全部',\n            icon: '🌟'\n        },\n        {\n            key: 'ai',\n            name: 'AI技术',\n            icon: '🤖'\n        },\n        {\n            key: 'architecture',\n            name: '建筑设计',\n            icon: '🏗️'\n        },\n        {\n            key: 'smart-city',\n            name: '智慧城市',\n            icon: '🏙️'\n        },\n        {\n            key: 'web3',\n            name: 'Web3',\n            icon: '🌐'\n        },\n        {\n            key: 'iot',\n            name: '物联网',\n            icon: '🔗'\n        },\n        {\n            key: 'vr',\n            name: 'VR/AR',\n            icon: '🥽'\n        }\n    ];\n    // 显示成功提示\n    const showToast = (message)=>{\n        setToastMessage(message);\n        setShowSuccessToast(true);\n        setTimeout(()=>setShowSuccessToast(false), 3000);\n    };\n    // 搜索和筛选功能\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DiscoveryPage.useEffect\": ()=>{\n            const filterContent = {\n                \"DiscoveryPage.useEffect.filterContent\": ()=>{\n                    const filtered = {};\n                    Object.keys(discoveryContent).forEach({\n                        \"DiscoveryPage.useEffect.filterContent\": (type)=>{\n                            filtered[type] = discoveryContent[type].filter({\n                                \"DiscoveryPage.useEffect.filterContent\": (item)=>{\n                                    // 搜索筛选\n                                    const matchesSearch = !searchQuery || item.title.toLowerCase().includes(searchQuery.toLowerCase()) || item.creator.toLowerCase().includes(searchQuery.toLowerCase()) || item.tags.some({\n                                        \"DiscoveryPage.useEffect.filterContent\": (tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase())\n                                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                                    // 分类筛选\n                                    const matchesCategory = selectedCategory === 'all' || item.tags.some({\n                                        \"DiscoveryPage.useEffect.filterContent\": (tag)=>{\n                                            switch(selectedCategory){\n                                                case 'ai':\n                                                    return tag.includes('AI') || tag.includes('智能');\n                                                case 'architecture':\n                                                    return tag.includes('建筑') || tag.includes('设计');\n                                                case 'smart-city':\n                                                    return tag.includes('智慧城市') || tag.includes('城市');\n                                                case 'web3':\n                                                    return tag.includes('Web3') || tag.includes('区块链') || tag.includes('NFT');\n                                                case 'iot':\n                                                    return tag.includes('物联网') || tag.includes('IoT');\n                                                case 'vr':\n                                                    return tag.includes('VR') || tag.includes('AR') || tag.includes('元宇宙');\n                                                default:\n                                                    return true;\n                                            }\n                                        }\n                                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                                    return matchesSearch && matchesCategory;\n                                }\n                            }[\"DiscoveryPage.useEffect.filterContent\"]);\n                        }\n                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                    setFilteredContent(filtered);\n                }\n            }[\"DiscoveryPage.useEffect.filterContent\"];\n            filterContent();\n        }\n    }[\"DiscoveryPage.useEffect\"], [\n        searchQuery,\n        selectedCategory\n    ]);\n    // 点赞功能\n    const handleLike = (itemId)=>{\n        setLikedItems((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(itemId)) {\n                newSet.delete(itemId);\n                showToast('取消点赞');\n            } else {\n                newSet.add(itemId);\n                showToast('点赞成功');\n            }\n            return newSet;\n        });\n    };\n    // 收藏功能\n    const handleSave = (itemId)=>{\n        setSavedItems((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(itemId)) {\n                newSet.delete(itemId);\n                showToast('取消收藏');\n            } else {\n                newSet.add(itemId);\n                showToast('收藏成功');\n            }\n            return newSet;\n        });\n    };\n    // 关注功能\n    const handleFollow = (userId)=>{\n        setFollowedUsers((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(userId)) {\n                newSet.delete(userId);\n                showToast('取消关注');\n            } else {\n                newSet.add(userId);\n                showToast('关注成功');\n            }\n            return newSet;\n        });\n    };\n    // 观看视频\n    const handleWatchVideo = (video)=>{\n        setSelectedContent(video);\n        setShowContentDetail(true);\n    };\n    // 阅读文章\n    const handleReadArticle = (article)=>{\n        setSelectedContent(article);\n        setShowContentDetail(true);\n    };\n    // 分享功能\n    const handleShare = (content)=>{\n        setSelectedContent(content);\n        setShowShareModal(true);\n    };\n    // 加入/退出群组\n    const handleJoinGroup = (groupId)=>{\n        setJoinedGroups((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(groupId)) {\n                newSet.delete(groupId);\n                showToast('已退出群组');\n            } else {\n                newSet.add(groupId);\n                showToast('成功加入群组');\n            }\n            return newSet;\n        });\n    };\n    // 查看项目详情\n    const handleViewProject = (project)=>{\n        setSelectedProject(project);\n        setShowContentDetail(true);\n    };\n    // 申请项目\n    const handleApplyProject = (project)=>{\n        setSelectedProject(project);\n        setShowApplyProject(true);\n    };\n    // NFT购买\n    const handleBuyNFT = (content)=>{\n        setSelectedContent(content);\n        setShowNFTModal(true);\n    };\n    // 创作者打赏\n    const handleTipCreator = (content)=>{\n        setSelectedContent(content);\n        setShowTipModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sticky top-0 bg-black/20 backdrop-blur-lg border-b border-white/10 p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-400 to-blue-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83D\\uDD0D\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"内容发现\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-purple-300\",\n                                                    children: \"探索 • 学习 • 连接\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400\",\n                                            children: \"●\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" 15.6k 在线\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        placeholder: \"搜索内容、创作者、标签...\",\n                                        className: \"w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 pl-10 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 bg-white/10 rounded-lg p-1 mb-4\",\n                            children: [\n                                {\n                                    key: 'video',\n                                    title: '视频内容',\n                                    icon: '🎬'\n                                },\n                                {\n                                    key: 'discovery',\n                                    title: '图文发现',\n                                    icon: '📰'\n                                },\n                                {\n                                    key: 'community',\n                                    title: '社群讨论',\n                                    icon: '👥'\n                                },\n                                {\n                                    key: 'ecosystem',\n                                    title: '生态匹配',\n                                    icon: '🤝'\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.key),\n                                    className: \"flex-1 flex flex-col items-center py-3 px-2 rounded-md text-xs font-medium transition-colors \".concat(activeTab === tab.key ? 'bg-purple-500 text-white' : 'text-white/70 hover:text-white'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg mb-1\",\n                                            children: tab.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tab.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, tab.key, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 overflow-x-auto pb-2\",\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedCategory(category.key),\n                                    className: \"flex-shrink-0 flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors \".concat(selectedCategory === category.key ? 'bg-green-500 text-white' : 'bg-white/10 text-gray-300 hover:bg-white/20'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: category.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, category.key, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 pb-20\",\n                    children: [\n                        activeTab === 'video' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: (filteredContent.video || discoveryContent.video).map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: video.thumbnail,\n                                                    alt: video.title,\n                                                    className: \"w-full h-48 object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-2 right-2 bg-black/70 px-2 py-1 rounded text-xs text-white\",\n                                                    children: video.duration\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 left-2 flex space-x-2\",\n                                                    children: [\n                                                        video.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-purple-500 px-2 py-1 rounded-full text-xs font-medium\",\n                                                            children: \"\\uD83C\\uDFA8 NFT\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        video.didVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-blue-500 px-2 py-1 rounded-full text-xs font-medium\",\n                                                            children: \"\\uD83C\\uDD94 DID\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                            children: video.avatar\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-bold text-white\",\n                                                                    children: video.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        video.creator,\n                                                                        \" • \",\n                                                                        video.publishTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-400 font-bold text-sm\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        video.rewards,\n                                                                        \" NGT\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: \"奖励\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm mb-3 line-clamp-2\",\n                                                    children: video.description\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mb-3\",\n                                                    children: video.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-purple-500/20 rounded text-xs text-purple-300\",\n                                                            children: [\n                                                                \"#\",\n                                                                tag\n                                                            ]\n                                                        }, idx, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"\\uD83D\\uDC41️ \",\n                                                                        video.views.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleLike(video.id),\n                                                                    className: \"flex items-center space-x-1 transition-colors \".concat(likedItems.has(video.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: likedItems.has(video.id) ? '❤️' : '🤍'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: video.likes + (likedItems.has(video.id) ? 1 : 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleSave(video.id),\n                                                                    className: \"transition-colors \".concat(savedItems.has(video.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'),\n                                                                    children: savedItems.has(video.id) ? '⭐' : '☆'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleShare(video),\n                                                                    className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                                    children: \"分享\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                video.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleBuyNFT(video),\n                                                                    className: \"px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors\",\n                                                                    children: \"购买NFT\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleWatchVideo(video),\n                                                                    className: \"px-3 py-1 bg-purple-500 rounded-lg text-xs font-medium hover:bg-purple-600 transition-colors\",\n                                                                    children: \"观看\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, video.id, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'discovery' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: (filteredContent.discovery || discoveryContent.discovery).map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-sm\",\n                                                                children: article.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        article.creator,\n                                                                        \" • \",\n                                                                        article.publishTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-400 font-bold text-sm\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        article.rewards,\n                                                                        \" NGT\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-white mb-2\",\n                                                        children: article.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-sm mb-3 line-clamp-3\",\n                                                        children: article.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mb-3\",\n                                                        children: article.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    tag\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCD6 \",\n                                                                            article.readTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDC41️ \",\n                                                                            article.views.toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleLike(article.id),\n                                                                        className: \"flex items-center space-x-1 transition-colors \".concat(likedItems.has(article.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: likedItems.has(article.id) ? '❤️' : '🤍'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 571,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: article.likes + (likedItems.has(article.id) ? 1 : 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 572,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleSave(article.id),\n                                                                        className: \"transition-colors \".concat(savedItems.has(article.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'),\n                                                                        children: savedItems.has(article.id) ? '⭐' : '☆'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleShare(article),\n                                                                        className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                                        children: \"分享\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    article.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleBuyNFT(article),\n                                                                        className: \"px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors\",\n                                                                        children: \"购买NFT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleReadArticle(article),\n                                                                        className: \"px-3 py-1 bg-blue-500 rounded-lg text-xs font-medium hover:bg-blue-600 transition-colors\",\n                                                                        children: \"阅读\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 21\n                                            }, this),\n                                            article.images && article.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 m-4 rounded-lg overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: article.images[0],\n                                                    alt: article.title,\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 19\n                                    }, this)\n                                }, article.id, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'community' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: (filteredContent.community || discoveryContent.community).map((community)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                            children: community.avatar\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-bold text-white\",\n                                                                    children: community.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        community.creator,\n                                                                        \" • \",\n                                                                        community.publishTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(community.activity === 'high' ? 'bg-green-400' : community.activity === 'medium' ? 'bg-yellow-400' : 'bg-gray-400')\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: community.activity === 'high' ? '活跃' : community.activity === 'medium' ? '一般' : '较少'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm mb-3\",\n                                            children: community.description\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                            children: community.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 bg-green-500/20 rounded text-xs text-green-300\",\n                                                    children: [\n                                                        \"#\",\n                                                        tag\n                                                    ]\n                                                }, idx, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"\\uD83D\\uDC65 \",\n                                                                community.members.toLocaleString(),\n                                                                \" 成员\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"\\uD83D\\uDCAC \",\n                                                                community.posts,\n                                                                \" 讨论\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300\",\n                                                            children: community.groupType\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleShare(community),\n                                                            className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                            children: \"分享\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleJoinGroup(community.id),\n                                                            className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(joinedGroups.has(community.id) ? 'bg-gray-600 text-gray-300 hover:bg-gray-500' : 'bg-gradient-to-r from-green-500 to-teal-500 text-white hover:from-green-600 hover:to-teal-600'),\n                                                            children: joinedGroups.has(community.id) ? '已加入' : '加入讨论'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, community.id, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'ecosystem' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: discoveryContent.ecosystem.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center text-xl\",\n                                                            children: project.avatar\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-bold text-white\",\n                                                                    children: project.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        project.creator,\n                                                                        \" • \",\n                                                                        project.publishTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(project.status === 'open' ? 'bg-green-500' : 'bg-gray-500'),\n                                                        children: project.status === 'open' ? '招募中' : '已结束'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm mb-3\",\n                                            children: project.description\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: \"预算范围\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-400 font-bold\",\n                                                            children: project.budget\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: \"项目周期\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-400 font-bold\",\n                                                            children: project.duration\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: \"项目地点\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-purple-400 font-bold\",\n                                                            children: project.location\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: \"匹配类型\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 732,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-orange-400 font-bold\",\n                                                            children: project.matchType\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400 mb-2\",\n                                                    children: \"技能要求\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: project.requirements.map((req, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-orange-500/20 rounded text-xs text-orange-300\",\n                                                            children: req\n                                                        }, idx, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                            children: project.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 bg-red-500/20 rounded text-xs text-red-300\",\n                                                    children: [\n                                                        \"#\",\n                                                        tag\n                                                    ]\n                                                }, idx, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-white/10 border border-white/20 rounded-lg text-sm font-medium hover:bg-white/20 transition-colors\",\n                                                    children: \"查看详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-colors\",\n                                                    children: \"立即申请\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, project.id, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n            lineNumber: 350,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n        lineNumber: 349,\n        columnNumber: 5\n    }, this);\n}\n_s(DiscoveryPage, \"p5BLHYhD7bS/EXdk2xWA/9sGK9M=\");\n_c = DiscoveryPage;\nvar _c;\n$RefreshReg$(_c, \"DiscoveryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/engineering/page.tsx\n"));

/***/ })

});