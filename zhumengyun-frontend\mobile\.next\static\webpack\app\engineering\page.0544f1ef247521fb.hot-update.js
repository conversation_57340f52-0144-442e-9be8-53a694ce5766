"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/engineering/page",{

/***/ "(app-pages-browser)/./src/app/engineering/page.tsx":
/*!**************************************!*\
  !*** ./src/app/engineering/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DiscoveryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DiscoveryPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('video');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // 交互状态\n    const [likedItems, setLikedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [followedUsers, setFollowedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [joinedGroups, setJoinedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'c1'\n    ]));\n    // 弹窗状态\n    const [showContentDetail, setShowContentDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showApplyProject, setShowApplyProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNFTModal, setShowNFTModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTipModal, setShowTipModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccessToast, setShowSuccessToast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toastMessage, setToastMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 选中的项目\n    const [selectedContent, setSelectedContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 加载状态\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 筛选后的内容\n    const [filteredContent, setFilteredContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 模拟发布的内容数据（对应发布页面的四大平台）\n    const discoveryContent = {\n        video: [\n            {\n                id: 'v1',\n                title: 'NextGen 2025智慧城市建设项目展示',\n                creator: '工程师·张三',\n                avatar: '👨‍💻',\n                thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',\n                duration: '3:45',\n                views: 12500,\n                likes: 890,\n                publishTime: '2小时前',\n                tags: [\n                    '智慧城市',\n                    'AI技术',\n                    '建筑设计'\n                ],\n                description: '展示了最新的AI驱动智慧城市建设项目，包括智能交通系统和物联网基础设施...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 120\n            },\n            {\n                id: 'v2',\n                title: 'AI辅助建筑设计全流程演示',\n                creator: '建筑师·王设计',\n                avatar: '🏗️',\n                thumbnail: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop',\n                duration: '5:20',\n                views: 8900,\n                likes: 567,\n                publishTime: '4小时前',\n                tags: [\n                    '建筑设计',\n                    'AI辅助',\n                    'BIM'\n                ],\n                description: '完整展示AI辅助建筑设计的全流程，从概念设计到施工图生成...',\n                nftEnabled: false,\n                didVerified: true,\n                rewards: 85\n            },\n            {\n                id: 'v3',\n                title: '元宇宙虚拟展厅设计案例',\n                creator: 'VR设计师·小李',\n                avatar: '🌌',\n                thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',\n                duration: '4:15',\n                views: 15600,\n                likes: 1234,\n                publishTime: '6小时前',\n                tags: [\n                    '元宇宙',\n                    'VR设计',\n                    '虚拟展厅'\n                ],\n                description: '创新的元宇宙虚拟展厅设计，支持多人实时交互和3D展示...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 150\n            }\n        ],\n        discovery: [\n            {\n                id: 'd1',\n                title: '2025年建筑行业AI应用趋势报告',\n                creator: '行业分析师·陈专家',\n                avatar: '📊',\n                images: [\n                    'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',\n                    'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop'\n                ],\n                readTime: '8分钟',\n                views: 5600,\n                likes: 234,\n                publishTime: '1小时前',\n                tags: [\n                    '行业报告',\n                    'AI应用',\n                    '建筑趋势'\n                ],\n                description: '深度分析2025年建筑行业AI应用的最新趋势，包括设计自动化、施工机器人等...',\n                nftEnabled: false,\n                didVerified: true,\n                rewards: 75\n            },\n            {\n                id: 'd2',\n                title: '智能建筑物联网系统设计指南',\n                creator: '物联网工程师·刘技术',\n                avatar: '🔗',\n                images: [\n                    'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop'\n                ],\n                readTime: '12分钟',\n                views: 3400,\n                likes: 189,\n                publishTime: '3小时前',\n                tags: [\n                    '物联网',\n                    '智能建筑',\n                    '系统设计'\n                ],\n                description: '详细介绍智能建筑物联网系统的设计原理、技术架构和实施方案...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 95\n            }\n        ],\n        community: [\n            {\n                id: 'c1',\n                title: '建筑师AI工具使用经验分享',\n                creator: '建筑师联盟',\n                avatar: '🏗️',\n                groupType: '技术讨论',\n                members: 2340,\n                posts: 156,\n                publishTime: '30分钟前',\n                tags: [\n                    '经验分享',\n                    'AI工具',\n                    '建筑师'\n                ],\n                description: '分享各种AI工具在建筑设计中的实际应用经验，包括Midjourney、Stable Diffusion等...',\n                isJoined: true,\n                activity: 'high'\n            },\n            {\n                id: 'c2',\n                title: 'Web3建设者技术讨论群',\n                creator: 'Web3建设者',\n                avatar: '🌐',\n                groupType: '技术交流',\n                members: 1567,\n                posts: 89,\n                publishTime: '1小时前',\n                tags: [\n                    'Web3',\n                    '区块链',\n                    '技术讨论'\n                ],\n                description: '讨论Web3技术在建筑和工程领域的应用，包括DeFi、NFT、DAO等...',\n                isJoined: false,\n                activity: 'medium'\n            }\n        ],\n        ecosystem: [\n            {\n                id: 'e1',\n                title: '寻求AI建筑设计合作伙伴',\n                creator: '建筑事务所·王总',\n                avatar: '🏢',\n                budget: '50-100万',\n                duration: '3-6个月',\n                location: '北京',\n                publishTime: '2小时前',\n                tags: [\n                    '项目合作',\n                    'AI建筑',\n                    '设计服务'\n                ],\n                description: '我们正在开发一个大型商业综合体项目，需要AI建筑设计方面的合作伙伴...',\n                requirements: [\n                    'AI设计经验',\n                    'BIM技术',\n                    '团队规模10+'\n                ],\n                matchType: '技术合作',\n                status: 'open'\n            },\n            {\n                id: 'e2',\n                title: '智慧城市项目寻求技术团队',\n                creator: '政府采购部门',\n                avatar: '🏛️',\n                budget: '200-500万',\n                duration: '6-12个月',\n                location: '上海',\n                publishTime: '4小时前',\n                tags: [\n                    '政府项目',\n                    '智慧城市',\n                    '技术团队'\n                ],\n                description: '智慧城市基础设施建设项目，需要具备AI、物联网、大数据技术的团队...',\n                requirements: [\n                    '政府项目经验',\n                    '资质齐全',\n                    '技术实力强'\n                ],\n                matchType: '工程项目匹配',\n                status: 'open'\n            }\n        ]\n    };\n    // 分类选项\n    const categories = [\n        {\n            key: 'all',\n            name: '全部',\n            icon: '🌟'\n        },\n        {\n            key: 'ai',\n            name: 'AI技术',\n            icon: '🤖'\n        },\n        {\n            key: 'architecture',\n            name: '建筑设计',\n            icon: '🏗️'\n        },\n        {\n            key: 'smart-city',\n            name: '智慧城市',\n            icon: '🏙️'\n        },\n        {\n            key: 'web3',\n            name: 'Web3',\n            icon: '🌐'\n        },\n        {\n            key: 'iot',\n            name: '物联网',\n            icon: '🔗'\n        },\n        {\n            key: 'vr',\n            name: 'VR/AR',\n            icon: '🥽'\n        }\n    ];\n    // 显示成功提示\n    const showToast = (message)=>{\n        setToastMessage(message);\n        setShowSuccessToast(true);\n        setTimeout(()=>setShowSuccessToast(false), 3000);\n    };\n    // 搜索和筛选功能\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DiscoveryPage.useEffect\": ()=>{\n            const filterContent = {\n                \"DiscoveryPage.useEffect.filterContent\": ()=>{\n                    const filtered = {};\n                    Object.keys(discoveryContent).forEach({\n                        \"DiscoveryPage.useEffect.filterContent\": (type)=>{\n                            filtered[type] = discoveryContent[type].filter({\n                                \"DiscoveryPage.useEffect.filterContent\": (item)=>{\n                                    // 搜索筛选\n                                    const matchesSearch = !searchQuery || item.title.toLowerCase().includes(searchQuery.toLowerCase()) || item.creator.toLowerCase().includes(searchQuery.toLowerCase()) || item.tags.some({\n                                        \"DiscoveryPage.useEffect.filterContent\": (tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase())\n                                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                                    // 分类筛选\n                                    const matchesCategory = selectedCategory === 'all' || item.tags.some({\n                                        \"DiscoveryPage.useEffect.filterContent\": (tag)=>{\n                                            switch(selectedCategory){\n                                                case 'ai':\n                                                    return tag.includes('AI') || tag.includes('智能');\n                                                case 'architecture':\n                                                    return tag.includes('建筑') || tag.includes('设计');\n                                                case 'smart-city':\n                                                    return tag.includes('智慧城市') || tag.includes('城市');\n                                                case 'web3':\n                                                    return tag.includes('Web3') || tag.includes('区块链') || tag.includes('NFT');\n                                                case 'iot':\n                                                    return tag.includes('物联网') || tag.includes('IoT');\n                                                case 'vr':\n                                                    return tag.includes('VR') || tag.includes('AR') || tag.includes('元宇宙');\n                                                default:\n                                                    return true;\n                                            }\n                                        }\n                                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                                    return matchesSearch && matchesCategory;\n                                }\n                            }[\"DiscoveryPage.useEffect.filterContent\"]);\n                        }\n                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                    setFilteredContent(filtered);\n                }\n            }[\"DiscoveryPage.useEffect.filterContent\"];\n            filterContent();\n        }\n    }[\"DiscoveryPage.useEffect\"], [\n        searchQuery,\n        selectedCategory\n    ]);\n    // 点赞功能\n    const handleLike = (itemId)=>{\n        setLikedItems((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(itemId)) {\n                newSet.delete(itemId);\n                showToast('取消点赞');\n            } else {\n                newSet.add(itemId);\n                showToast('点赞成功');\n            }\n            return newSet;\n        });\n    };\n    // 收藏功能\n    const handleSave = (itemId)=>{\n        setSavedItems((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(itemId)) {\n                newSet.delete(itemId);\n                showToast('取消收藏');\n            } else {\n                newSet.add(itemId);\n                showToast('收藏成功');\n            }\n            return newSet;\n        });\n    };\n    // 关注功能\n    const handleFollow = (userId)=>{\n        setFollowedUsers((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(userId)) {\n                newSet.delete(userId);\n                showToast('取消关注');\n            } else {\n                newSet.add(userId);\n                showToast('关注成功');\n            }\n            return newSet;\n        });\n    };\n    // 观看视频\n    const handleWatchVideo = (video)=>{\n        setSelectedContent(video);\n        setShowContentDetail(true);\n    };\n    // 阅读文章\n    const handleReadArticle = (article)=>{\n        setSelectedContent(article);\n        setShowContentDetail(true);\n    };\n    // 分享功能\n    const handleShare = (content)=>{\n        setSelectedContent(content);\n        setShowShareModal(true);\n    };\n    // 加入/退出群组\n    const handleJoinGroup = (groupId)=>{\n        setJoinedGroups((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(groupId)) {\n                newSet.delete(groupId);\n                showToast('已退出群组');\n            } else {\n                newSet.add(groupId);\n                showToast('成功加入群组');\n            }\n            return newSet;\n        });\n    };\n    // 查看项目详情\n    const handleViewProject = (project)=>{\n        setSelectedProject(project);\n        setShowContentDetail(true);\n    };\n    // 申请项目\n    const handleApplyProject = (project)=>{\n        setSelectedProject(project);\n        setShowApplyProject(true);\n    };\n    // NFT购买\n    const handleBuyNFT = (content)=>{\n        setSelectedContent(content);\n        setShowNFTModal(true);\n    };\n    // 创作者打赏\n    const handleTipCreator = (content)=>{\n        setSelectedContent(content);\n        setShowTipModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sticky top-0 bg-black/20 backdrop-blur-lg border-b border-white/10 p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-400 to-blue-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83D\\uDD0D\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"内容发现\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-purple-300\",\n                                                    children: \"探索 • 学习 • 连接\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400\",\n                                            children: \"●\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" 15.6k 在线\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        placeholder: \"搜索内容、创作者、标签...\",\n                                        className: \"w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 pl-10 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 bg-white/10 rounded-lg p-1 mb-4\",\n                            children: [\n                                {\n                                    key: 'video',\n                                    title: '视频内容',\n                                    icon: '🎬'\n                                },\n                                {\n                                    key: 'discovery',\n                                    title: '图文发现',\n                                    icon: '📰'\n                                },\n                                {\n                                    key: 'community',\n                                    title: '社群讨论',\n                                    icon: '👥'\n                                },\n                                {\n                                    key: 'ecosystem',\n                                    title: '生态匹配',\n                                    icon: '🤝'\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.key),\n                                    className: \"flex-1 flex flex-col items-center py-3 px-2 rounded-md text-xs font-medium transition-colors \".concat(activeTab === tab.key ? 'bg-purple-500 text-white' : 'text-white/70 hover:text-white'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg mb-1\",\n                                            children: tab.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tab.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, tab.key, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 overflow-x-auto pb-2\",\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedCategory(category.key),\n                                    className: \"flex-shrink-0 flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors \".concat(selectedCategory === category.key ? 'bg-green-500 text-white' : 'bg-white/10 text-gray-300 hover:bg-white/20'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: category.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, category.key, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 pb-20\",\n                    children: [\n                        activeTab === 'video' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: (filteredContent.video || discoveryContent.video).map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: video.thumbnail,\n                                                    alt: video.title,\n                                                    className: \"w-full h-48 object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-2 right-2 bg-black/70 px-2 py-1 rounded text-xs text-white\",\n                                                    children: video.duration\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-2 left-2 flex space-x-2\",\n                                                    children: [\n                                                        video.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-purple-500 px-2 py-1 rounded-full text-xs font-medium\",\n                                                            children: \"\\uD83C\\uDFA8 NFT\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        video.didVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-blue-500 px-2 py-1 rounded-full text-xs font-medium\",\n                                                            children: \"\\uD83C\\uDD94 DID\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                            children: video.avatar\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-bold text-white\",\n                                                                    children: video.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        video.creator,\n                                                                        \" • \",\n                                                                        video.publishTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-400 font-bold text-sm\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        video.rewards,\n                                                                        \" NGT\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: \"奖励\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm mb-3 line-clamp-2\",\n                                                    children: video.description\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mb-3\",\n                                                    children: video.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-purple-500/20 rounded text-xs text-purple-300\",\n                                                            children: [\n                                                                \"#\",\n                                                                tag\n                                                            ]\n                                                        }, idx, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"\\uD83D\\uDC41️ \",\n                                                                        video.views.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleLike(video.id),\n                                                                    className: \"flex items-center space-x-1 transition-colors \".concat(likedItems.has(video.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: likedItems.has(video.id) ? '❤️' : '🤍'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: video.likes + (likedItems.has(video.id) ? 1 : 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleSave(video.id),\n                                                                    className: \"transition-colors \".concat(savedItems.has(video.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'),\n                                                                    children: savedItems.has(video.id) ? '⭐' : '☆'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleShare(video),\n                                                                    className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                                    children: \"分享\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                video.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleBuyNFT(video),\n                                                                    className: \"px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors\",\n                                                                    children: \"购买NFT\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleWatchVideo(video),\n                                                                    className: \"px-3 py-1 bg-purple-500 rounded-lg text-xs font-medium hover:bg-purple-600 transition-colors\",\n                                                                    children: \"观看\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, video.id, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'discovery' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: (filteredContent.discovery || discoveryContent.discovery).map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-sm\",\n                                                                children: article.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        article.creator,\n                                                                        \" • \",\n                                                                        article.publishTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-400 font-bold text-sm\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        article.rewards,\n                                                                        \" NGT\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-white mb-2\",\n                                                        children: article.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-sm mb-3 line-clamp-3\",\n                                                        children: article.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mb-3\",\n                                                        children: article.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    tag\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCD6 \",\n                                                                            article.readTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDC41️ \",\n                                                                            article.views.toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleLike(article.id),\n                                                                        className: \"flex items-center space-x-1 transition-colors \".concat(likedItems.has(article.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: likedItems.has(article.id) ? '❤️' : '🤍'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 571,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: article.likes + (likedItems.has(article.id) ? 1 : 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 572,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleSave(article.id),\n                                                                        className: \"transition-colors \".concat(savedItems.has(article.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'),\n                                                                        children: savedItems.has(article.id) ? '⭐' : '☆'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleShare(article),\n                                                                        className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                                        children: \"分享\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    article.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleBuyNFT(article),\n                                                                        className: \"px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors\",\n                                                                        children: \"购买NFT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleReadArticle(article),\n                                                                        className: \"px-3 py-1 bg-blue-500 rounded-lg text-xs font-medium hover:bg-blue-600 transition-colors\",\n                                                                        children: \"阅读\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 21\n                                            }, this),\n                                            article.images && article.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 m-4 rounded-lg overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: article.images[0],\n                                                    alt: article.title,\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 19\n                                    }, this)\n                                }, article.id, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'community' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: (filteredContent.community || discoveryContent.community).map((community)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                            children: community.avatar\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-bold text-white\",\n                                                                    children: community.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        community.creator,\n                                                                        \" • \",\n                                                                        community.publishTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(community.activity === 'high' ? 'bg-green-400' : community.activity === 'medium' ? 'bg-yellow-400' : 'bg-gray-400')\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: community.activity === 'high' ? '活跃' : community.activity === 'medium' ? '一般' : '较少'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm mb-3\",\n                                            children: community.description\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                            children: community.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 bg-green-500/20 rounded text-xs text-green-300\",\n                                                    children: [\n                                                        \"#\",\n                                                        tag\n                                                    ]\n                                                }, idx, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"\\uD83D\\uDC65 \",\n                                                                community.members.toLocaleString(),\n                                                                \" 成员\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"\\uD83D\\uDCAC \",\n                                                                community.posts,\n                                                                \" 讨论\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300\",\n                                                            children: community.groupType\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleShare(community),\n                                                            className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                            children: \"分享\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleJoinGroup(community.id),\n                                                            className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(joinedGroups.has(community.id) ? 'bg-gray-600 text-gray-300 hover:bg-gray-500' : 'bg-gradient-to-r from-green-500 to-teal-500 text-white hover:from-green-600 hover:to-teal-600'),\n                                                            children: joinedGroups.has(community.id) ? '已加入' : '加入讨论'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, community.id, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'ecosystem' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: (filteredContent.ecosystem || discoveryContent.ecosystem).map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center text-xl\",\n                                                            children: project.avatar\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-bold text-white\",\n                                                                    children: project.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        project.creator,\n                                                                        \" • \",\n                                                                        project.publishTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(project.status === 'open' ? 'bg-green-500' : 'bg-gray-500'),\n                                                        children: project.status === 'open' ? '招募中' : '已结束'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm mb-3\",\n                                            children: project.description\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: \"预算范围\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-400 font-bold\",\n                                                            children: project.budget\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: \"项目周期\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-400 font-bold\",\n                                                            children: project.duration\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: \"项目地点\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-purple-400 font-bold\",\n                                                            children: project.location\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: \"匹配类型\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 732,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-orange-400 font-bold\",\n                                                            children: project.matchType\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400 mb-2\",\n                                                    children: \"技能要求\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: project.requirements.map((req, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-orange-500/20 rounded text-xs text-orange-300\",\n                                                            children: req\n                                                        }, idx, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                            children: project.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 bg-red-500/20 rounded text-xs text-red-300\",\n                                                    children: [\n                                                        \"#\",\n                                                        tag\n                                                    ]\n                                                }, idx, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-white/10 border border-white/20 rounded-lg text-sm font-medium hover:bg-white/20 transition-colors\",\n                                                    children: \"查看详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-colors\",\n                                                    children: \"立即申请\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, project.id, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n            lineNumber: 350,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n        lineNumber: 349,\n        columnNumber: 5\n    }, this);\n}\n_s(DiscoveryPage, \"p5BLHYhD7bS/EXdk2xWA/9sGK9M=\");\n_c = DiscoveryPage;\nvar _c;\n$RefreshReg$(_c, \"DiscoveryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/engineering/page.tsx\n"));

/***/ })

});