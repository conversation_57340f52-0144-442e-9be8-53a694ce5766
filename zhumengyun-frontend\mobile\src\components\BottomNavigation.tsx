'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

export default function BottomNavigation() {
  const pathname = usePathname()

  const navItems = [
    {
      href: '/',
      icon: 'home',
      label: '首页',
      active: pathname === '/'
    },
    {
      href: '/engineering',
      icon: 'discover',
      label: '发现',
      active: pathname === '/engineering'
    },
    {
      href: '/creator',
      icon: 'create',
      label: '',
      active: pathname === '/creator',
      isCreate: true
    },
    {
      href: '/social',
      icon: 'messages',
      label: '消息',
      active: pathname === '/social'
    },
    {
      href: '/profile',
      icon: 'profile',
      label: '我的',
      active: pathname === '/profile'
    }
  ]

  // TikTok风格的SVG图标
  const renderIcon = (iconType: string, isActive: boolean) => {
    const iconClass = `w-7 h-7 ${isActive ? 'text-white' : 'text-gray-500'}`;

    switch (iconType) {
      case 'home':
        return (
          <svg className={iconClass} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24" strokeWidth={isActive ? 0 : 2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
        );
      case 'discover':
        return (
          <svg className={iconClass} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24" strokeWidth={isActive ? 0 : 2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        );
      case 'create':
        return (
          <div className="relative">
            <div className="w-12 h-8 bg-gradient-to-r from-red-500 via-pink-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={3}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
          </div>
        );
      case 'messages':
        return (
          <svg className={iconClass} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24" strokeWidth={isActive ? 0 : 2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      case 'profile':
        return (
          <svg className={iconClass} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24" strokeWidth={isActive ? 0 : 2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      default:
        return null;
    }
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      <div className="flex items-center justify-around py-1 pb-safe">
        {navItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className="flex flex-col items-center py-2 px-2 transition-all duration-200"
          >
            <div className={`transition-all duration-200 ${
              item.active && !item.isCreate ? 'scale-110' : ''
            }`}>
              {renderIcon(item.icon, item.active)}
            </div>
            {item.label && (
              <span className={`text-xs mt-1 transition-colors duration-200 ${
                item.active ? 'text-white font-medium' : 'text-gray-500'
              }`}>
                {item.label}
              </span>
            )}
          </Link>
        ))}
      </div>
    </div>
  )
}
