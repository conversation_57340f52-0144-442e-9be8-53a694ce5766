"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/social/page",{

/***/ "(app-pages-browser)/./src/app/social/page.tsx":
/*!*********************************!*\
  !*** ./src/app/social/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SocialPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SocialPage() {\n    _s();\n    const [spaces, setSpaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('spaces');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedChatRoom, setSelectedChatRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [showCreateSpace, setShowCreateSpace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProfile, setShowProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [onlineUsers, setOnlineUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '工程师·张三',\n        avatar: '👨‍💻',\n        level: 5,\n        ngtBalance: 1250,\n        reputation: 95,\n        followers: 2340,\n        following: 890,\n        verified: true,\n        did: 'did:ngt:0x1234...5678'\n    });\n    // 聊天室数据\n    const chatRooms = [\n        {\n            id: 'general',\n            name: '综合讨论',\n            icon: '💬',\n            members: 1234,\n            description: '工程师和创作者的综合交流空间',\n            type: 'public',\n            lastMessage: {\n                user: '建筑师小王',\n                content: '刚完成了一个AI辅助的建筑设计',\n                time: '2分钟前'\n            }\n        },\n        {\n            id: 'tech',\n            name: '技术讨论',\n            icon: '⚙️',\n            members: 890,\n            description: '技术分享和问题讨论',\n            type: 'public',\n            lastMessage: {\n                user: 'AI专家',\n                content: '最新的GPT模型在工程设计中的应用',\n                time: '5分钟前'\n            }\n        },\n        {\n            id: 'nft',\n            name: 'NFT创作',\n            icon: '🎨',\n            members: 567,\n            description: 'NFT创作和交易讨论',\n            type: 'public',\n            lastMessage: {\n                user: '数字艺术家',\n                content: '分享一个新的NFT作品',\n                time: '10分钟前'\n            }\n        },\n        {\n            id: 'dao',\n            name: 'DAO治理',\n            icon: '🏛️',\n            members: 456,\n            description: '平台治理和提案讨论',\n            type: 'premium',\n            lastMessage: {\n                user: '社区管理员',\n                content: '新提案：降低交易手续费',\n                time: '15分钟前'\n            }\n        }\n    ];\n    // 社区群组数据\n    const communityGroups = [\n        {\n            id: 'architects',\n            name: '建筑师联盟',\n            icon: '🏗️',\n            members: 2340,\n            category: 'professional',\n            description: '全球建筑师专业交流社区',\n            tags: [\n                'BIM',\n                'AI设计',\n                '可持续建筑'\n            ],\n            isJoined: true,\n            activity: 'high'\n        },\n        {\n            id: 'ai-creators',\n            name: 'AI创作者',\n            icon: '🤖',\n            members: 1890,\n            category: 'creative',\n            description: 'AI辅助创作和艺术探索',\n            tags: [\n                'AI艺术',\n                'Midjourney',\n                'Stable Diffusion'\n            ],\n            isJoined: true,\n            activity: 'high'\n        },\n        {\n            id: 'web3-builders',\n            name: 'Web3建设者',\n            icon: '🌐',\n            members: 1567,\n            category: 'technology',\n            description: '区块链和Web3技术讨论',\n            tags: [\n                'DeFi',\n                'NFT',\n                'DAO'\n            ],\n            isJoined: false,\n            activity: 'medium'\n        },\n        {\n            id: 'metaverse-designers',\n            name: '元宇宙设计师',\n            icon: '🌌',\n            members: 1234,\n            category: 'design',\n            description: '虚拟世界设计和体验创新',\n            tags: [\n                'VR',\n                'AR',\n                '3D设计'\n            ],\n            isJoined: true,\n            activity: 'high'\n        }\n    ];\n    // NFT市场数据\n    const nftMarketplace = [\n        {\n            id: 'nft-1',\n            title: '未来城市建筑设计',\n            creator: '建筑大师·王设计',\n            price: '0.5 ETH',\n            image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',\n            likes: 234,\n            category: 'architecture',\n            rarity: 'rare',\n            verified: true\n        },\n        {\n            id: 'nft-2',\n            title: 'AI生成艺术作品',\n            creator: 'AI艺术家·小创',\n            price: '0.3 ETH',\n            image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=300&fit=crop',\n            likes: 456,\n            category: 'ai-art',\n            rarity: 'epic',\n            verified: true\n        },\n        {\n            id: 'nft-3',\n            title: '智慧城市概念图',\n            creator: '城市规划师',\n            price: '0.8 ETH',\n            image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=300&h=300&fit=crop',\n            likes: 189,\n            category: 'concept',\n            rarity: 'legendary',\n            verified: true\n        }\n    ];\n    const mockSpaces = [\n        {\n            id: '1',\n            title: '未来建筑师聚会空间',\n            description: '专为建筑师和设计师打造的虚拟聚会空间，支持3D模型展示、实时协作设计和专业交流。',\n            host: {\n                name: '建筑大师·王设计',\n                avatar: '🏛️',\n                verified: true,\n                followers: 15600,\n                reputation: 95\n            },\n            stats: {\n                participants: 234,\n                likes: 1890,\n                comments: 456,\n                shares: 123\n            },\n            tags: [\n                '建筑设计',\n                'VR协作',\n                '专业交流',\n                '3D展示',\n                '设计师社区'\n            ],\n            media: {\n                type: 'vr-space',\n                url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',\n                modelUrl: '/models/architect-space.glb'\n            },\n            spaceType: 'public',\n            capacity: 500,\n            currentUsers: 234,\n            features: [\n                '3D模型展示',\n                '语音聊天',\n                '屏幕共享',\n                'AI助手',\n                '实时协作'\n            ],\n            location: '虚拟建筑学院',\n            startTime: '2025-01-15T19:00:00Z'\n        },\n        {\n            id: '2',\n            title: 'AI创作者元宇宙派对',\n            description: 'AI艺术家和创作者的专属聚会空间，展示最新AI生成艺术作品，交流创作技巧和商业合作。',\n            host: {\n                name: 'AI艺术家·小创',\n                avatar: '🎨',\n                verified: true,\n                followers: 28900,\n                reputation: 88\n            },\n            stats: {\n                participants: 567,\n                likes: 3450,\n                comments: 890,\n                shares: 234\n            },\n            tags: [\n                'AI艺术',\n                '创作者经济',\n                'NFT展示',\n                '商业合作',\n                '技术交流'\n            ],\n            media: {\n                type: 'metaverse-event',\n                url: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',\n                modelUrl: '/models/creator-party.glb'\n            },\n            spaceType: 'event',\n            capacity: 1000,\n            currentUsers: 567,\n            features: [\n                'NFT画廊',\n                '音乐DJ',\n                '互动游戏',\n                '商务洽谈',\n                'AI生成艺术'\n            ],\n            location: '创作者元宇宙中心',\n            startTime: '2025-01-15T20:00:00Z'\n        },\n        {\n            id: '3',\n            title: '工程师技术分享会',\n            description: '全球工程师的技术分享和学习空间，讨论最新技术趋势、开源项目和职业发展。',\n            host: {\n                name: '技术专家·李工程师',\n                avatar: '⚙️',\n                verified: true,\n                followers: 45200,\n                reputation: 92\n            },\n            stats: {\n                participants: 890,\n                likes: 5670,\n                comments: 1234,\n                shares: 456\n            },\n            tags: [\n                '技术分享',\n                '开源项目',\n                '职业发展',\n                '编程技术',\n                '工程师社区'\n            ],\n            media: {\n                type: 'virtual-room',\n                url: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',\n                modelUrl: '/models/tech-meetup.glb'\n            },\n            spaceType: 'public',\n            capacity: 2000,\n            currentUsers: 890,\n            features: [\n                '代码演示',\n                '技术讲座',\n                '项目展示',\n                '招聘信息',\n                '导师指导'\n            ],\n            location: '全球技术中心',\n            startTime: '2025-01-15T21:00:00Z'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocialPage.useEffect\": ()=>{\n            setLoading(true);\n            setTimeout({\n                \"SocialPage.useEffect\": ()=>{\n                    setSpaces(mockSpaces);\n                    setLoading(false);\n                }\n            }[\"SocialPage.useEffect\"], 1000);\n        }\n    }[\"SocialPage.useEffect\"], []);\n    const formatNumber = (num)=>{\n        if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';\n        if (num >= 1000) return (num / 1000).toFixed(1) + 'k';\n        return num.toString();\n    };\n    const getSpaceTypeColor = (type)=>{\n        switch(type){\n            case 'public':\n                return 'bg-green-500';\n            case 'private':\n                return 'bg-red-500';\n            case 'premium':\n                return 'bg-yellow-500';\n            case 'event':\n                return 'bg-purple-500';\n            default:\n                return 'bg-gray-500';\n        }\n    };\n    const getSpaceTypeText = (type)=>{\n        switch(type){\n            case 'public':\n                return '公开';\n            case 'private':\n                return '私密';\n            case 'premium':\n                return '高级';\n            case 'event':\n                return '活动';\n            default:\n                return '未知';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-green-400 to-teal-500 rounded-2xl flex items-center justify-center mb-4 mx-auto animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white font-bold text-2xl\",\n                            children: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-lg mb-2\",\n                        children: \"社交元宇宙\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-sm\",\n                        children: \"连接虚拟空间中...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n            lineNumber: 337,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-10 flex flex-col h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-black/20 backdrop-blur-lg border-b border-white/10 p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-green-400 to-teal-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-sm\",\n                                                children: \"\\uD83D\\uDE80\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: \"社交元宇宙\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-300\",\n                                                    children: \"虚拟空间 • 实时聊天 • 社交网络\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-white/20 rounded-full px-2 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs\",\n                                                children: \"1.2k 在线\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 bg-white/10 rounded-lg p-1 overflow-x-auto\",\n                            children: [\n                                {\n                                    key: 'spaces',\n                                    label: '虚拟空间',\n                                    icon: '🌌'\n                                },\n                                {\n                                    key: 'chat',\n                                    label: '实时聊天',\n                                    icon: '💬'\n                                },\n                                {\n                                    key: 'community',\n                                    label: '社区群组',\n                                    icon: '👥'\n                                },\n                                {\n                                    key: 'friends',\n                                    label: '好友动态',\n                                    icon: '🤝'\n                                },\n                                {\n                                    key: 'nft',\n                                    label: 'NFT市场',\n                                    icon: '🎨'\n                                },\n                                {\n                                    key: 'dao',\n                                    label: 'DAO治理',\n                                    icon: '🏛️'\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.key),\n                                    className: \"flex-shrink-0 flex items-center justify-center space-x-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === tab.key ? 'bg-green-500 text-white' : 'text-white/70 hover:text-white'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tab.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"whitespace-nowrap\",\n                                            children: tab.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, tab.key, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden\",\n                    children: [\n                        activeTab === 'spaces' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: spaces.map((space, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                children: space.host.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: space.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-green-300\",\n                                                                        children: space.host.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 text-xs text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            space.currentUsers,\n                                                                            \"/\",\n                                                                            space.capacity\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 \".concat(getSpaceTypeColor(space.spaceType), \" rounded-full text-xs font-medium mt-1 inline-block\"),\n                                                                children: getSpaceTypeText(space.spaceType)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300 mb-3\",\n                                                children: space.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: space.features.slice(0, 2).map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-green-500/20 rounded text-xs\",\n                                                                children: feature\n                                                            }, idx, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"px-4 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium\",\n                                                        children: \"\\uD83D\\uDE80 进入空间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 19\n                                    }, this)\n                                }, space.id, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto p-4 space-y-3\",\n                                    children: [\n                                        {\n                                            id: 1,\n                                            user: '🏗️ 建筑师小王',\n                                            message: '刚完成了一个AI辅助的建筑设计，效果很棒！',\n                                            time: '2分钟前',\n                                            isMe: false\n                                        },\n                                        {\n                                            id: 2,\n                                            user: '我',\n                                            message: '能分享一下设计图吗？很想看看AI的效果',\n                                            time: '1分钟前',\n                                            isMe: true\n                                        },\n                                        {\n                                            id: 3,\n                                            user: '🎨 AI艺术家',\n                                            message: '我也在用AI创作NFT，最近很火呢',\n                                            time: '30秒前',\n                                            isMe: false\n                                        }\n                                    ].map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex \".concat(msg.isMe ? 'justify-end' : 'justify-start'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[80%] \".concat(msg.isMe ? 'bg-green-500' : 'bg-white/10', \" rounded-lg p-3\"),\n                                                children: [\n                                                    !msg.isMe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-300 mb-1\",\n                                                        children: msg.user\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-white\",\n                                                        children: msg.message\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: msg.time\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, msg.id, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 pb-20 border-t border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newMessage,\n                                                onChange: (e)=>setNewMessage(e.target.value),\n                                                placeholder: \"输入消息...\",\n                                                className: \"flex-1 bg-white/10 text-white px-4 py-3 rounded-full border border-white/20 focus:border-green-500 focus:outline-none\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl\",\n                                                    children: \"\\uD83D\\uDE80\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'friends' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                {\n                                    id: 1,\n                                    user: '🏗️ 建筑师小王',\n                                    action: '发布了新的BIM模型',\n                                    content: 'AI驱动的智慧建筑设计',\n                                    time: '5分钟前',\n                                    likes: 23\n                                },\n                                {\n                                    id: 2,\n                                    user: '🎨 AI艺术家',\n                                    action: '创建了NFT作品',\n                                    content: '赛博朋克风格的未来城市',\n                                    time: '15分钟前',\n                                    likes: 45\n                                },\n                                {\n                                    id: 3,\n                                    user: '🚀 元宇宙设计师',\n                                    action: '加入了虚拟空间',\n                                    content: '建筑师专业交流会',\n                                    time: '30分钟前',\n                                    likes: 12\n                                }\n                            ].map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: activity.user.split(' ')[0]\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: activity.user.split(' ')[1]\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: [\n                                                                    \" \",\n                                                                    activity.action\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-300 mt-1\",\n                                                        children: activity.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 mt-2 text-xs text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: activity.time\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"flex items-center space-x-1 text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"❤️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 519,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: activity.likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 520,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"\\uD83D\\uDCAC 回复\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 19\n                                    }, this)\n                                }, activity.id, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'dao' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83C\\uDFDB️\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"NextGen DAO\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-400\",\n                                                            children: \"25,680\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-green-200\",\n                                                            children: \"NGT持有者\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-400\",\n                                                            children: \"156\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-blue-200\",\n                                                            children: \"活跃提案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-400\",\n                                                            children: \"89%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: \"参与率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-yellow-400\",\n                                                            children: \"4.8M\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-yellow-200\",\n                                                            children: \"总投票权\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/5 rounded-lg p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300\",\n                                                children: \"NextGen DAO是一个去中心化自治组织，由社区成员共同治理平台的发展方向。 持有NGT代币即可参与提案投票，共同决定平台的未来。\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"活跃提案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                {\n                                                    id: 'prop-042',\n                                                    title: '降低平台交易手续费至2%',\n                                                    description: '建议将NFT交易手续费从3%降低至2%，以提高平台竞争力',\n                                                    status: 'voting',\n                                                    votes: {\n                                                        for: 15420,\n                                                        against: 3280\n                                                    },\n                                                    endTime: '2025-01-20',\n                                                    category: 'economic'\n                                                },\n                                                {\n                                                    id: 'prop-043',\n                                                    title: '新增VR虚拟展厅功能',\n                                                    description: '为创作者提供VR虚拟展厅，展示NFT作品集',\n                                                    status: 'voting',\n                                                    votes: {\n                                                        for: 12890,\n                                                        against: 1560\n                                                    },\n                                                    endTime: '2025-01-22',\n                                                    category: 'feature'\n                                                },\n                                                {\n                                                    id: 'prop-044',\n                                                    title: '建立创作者扶持基金',\n                                                    description: '从平台收益中拨出10%建立创作者扶持基金',\n                                                    status: 'passed',\n                                                    votes: {\n                                                        for: 18920,\n                                                        against: 2340\n                                                    },\n                                                    endTime: '2025-01-15',\n                                                    category: 'community'\n                                                }\n                                            ].map((proposal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: proposal.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(proposal.status === 'voting' ? 'bg-yellow-500' : proposal.status === 'passed' ? 'bg-green-500' : 'bg-red-500'),\n                                                                    children: proposal.status === 'voting' ? '投票中' : proposal.status === 'passed' ? '已通过' : '未通过'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-300 mb-3\",\n                                                            children: proposal.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-xs mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-400\",\n                                                                            children: [\n                                                                                \"赞成: \",\n                                                                                proposal.votes.for.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: [\n                                                                                \"反对: \",\n                                                                                proposal.votes.against.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 617,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 615,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-white/20 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full\",\n                                                                        style: {\n                                                                            width: \"\".concat(proposal.votes.for / (proposal.votes.for + proposal.votes.against) * 100, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: proposal.status === 'voting' ? \"截止: \".concat(proposal.endTime) : \"结束: \".concat(proposal.endTime)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                proposal.status === 'voting' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"px-3 py-1 bg-green-500 rounded-lg text-xs font-medium\",\n                                                                            children: \"赞成\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 635,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"px-3 py-1 bg-red-500 rounded-lg text-xs font-medium\",\n                                                                            children: \"反对\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, proposal.id, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"专业委员会\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3\",\n                                            children: [\n                                                {\n                                                    name: '技术委员会',\n                                                    icon: '⚙️',\n                                                    members: 12,\n                                                    description: '技术路线制定'\n                                                },\n                                                {\n                                                    name: '内容委员会',\n                                                    icon: '📝',\n                                                    members: 8,\n                                                    description: '内容质量监管'\n                                                },\n                                                {\n                                                    name: '经济委员会',\n                                                    icon: '💰',\n                                                    members: 10,\n                                                    description: '代币经济设计'\n                                                },\n                                                {\n                                                    name: '仲裁委员会',\n                                                    icon: '⚖️',\n                                                    members: 6,\n                                                    description: '争议处理'\n                                                }\n                                            ].map((committee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: committee.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-white text-sm\",\n                                                                    children: committee.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mb-2\",\n                                                            children: committee.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-400\",\n                                                                    children: [\n                                                                        committee.members,\n                                                                        \" 成员\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-xs text-blue-400 hover:text-blue-300\",\n                                                                    children: \"查看详情\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, committee.name, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"我的治理参与\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 676,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-purple-400\",\n                                                            children: \"1,250\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: \"投票权重\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-green-400\",\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-green-200\",\n                                                            children: \"参与提案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-yellow-400\",\n                                                            children: \"89%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-yellow-200\",\n                                                            children: \"参与率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium\",\n                                                    children: \"创建提案\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-sm font-medium\",\n                                                    children: \"委托投票\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 675,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n            lineNumber: 352,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n        lineNumber: 350,\n        columnNumber: 5\n    }, this);\n}\n_s(SocialPage, \"oYvQcbLGgnoxv9j60RjZreD36jU=\");\n_c = SocialPage;\nvar _c;\n$RefreshReg$(_c, \"SocialPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/social/page.tsx\n"));

/***/ })

});