"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/social/page",{

/***/ "(app-pages-browser)/./src/app/social/page.tsx":
/*!*********************************!*\
  !*** ./src/app/social/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SocialPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SocialPage() {\n    var _chatRooms_find, _chatRooms_find1, _chatRooms_find2, _chatRooms_find3, _chatRooms_find4;\n    _s();\n    const [spaces, setSpaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('spaces');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedChatRoom, setSelectedChatRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [showCreateSpace, setShowCreateSpace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProfile, setShowProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [onlineUsers, setOnlineUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '工程师·张三',\n        avatar: '👨‍💻',\n        level: 5,\n        ngtBalance: 1250,\n        reputation: 95,\n        followers: 2340,\n        following: 890,\n        verified: true,\n        did: 'did:ngt:0x1234...5678'\n    });\n    // 聊天室数据\n    const chatRooms = [\n        {\n            id: 'general',\n            name: '综合讨论',\n            icon: '💬',\n            members: 1234,\n            description: '工程师和创作者的综合交流空间',\n            type: 'public',\n            lastMessage: {\n                user: '建筑师小王',\n                content: '刚完成了一个AI辅助的建筑设计',\n                time: '2分钟前'\n            }\n        },\n        {\n            id: 'tech',\n            name: '技术讨论',\n            icon: '⚙️',\n            members: 890,\n            description: '技术分享和问题讨论',\n            type: 'public',\n            lastMessage: {\n                user: 'AI专家',\n                content: '最新的GPT模型在工程设计中的应用',\n                time: '5分钟前'\n            }\n        },\n        {\n            id: 'nft',\n            name: 'NFT创作',\n            icon: '🎨',\n            members: 567,\n            description: 'NFT创作和交易讨论',\n            type: 'public',\n            lastMessage: {\n                user: '数字艺术家',\n                content: '分享一个新的NFT作品',\n                time: '10分钟前'\n            }\n        },\n        {\n            id: 'dao',\n            name: 'DAO治理',\n            icon: '🏛️',\n            members: 456,\n            description: '平台治理和提案讨论',\n            type: 'premium',\n            lastMessage: {\n                user: '社区管理员',\n                content: '新提案：降低交易手续费',\n                time: '15分钟前'\n            }\n        }\n    ];\n    // 社区群组数据\n    const communityGroups = [\n        {\n            id: 'architects',\n            name: '建筑师联盟',\n            icon: '🏗️',\n            members: 2340,\n            category: 'professional',\n            description: '全球建筑师专业交流社区',\n            tags: [\n                'BIM',\n                'AI设计',\n                '可持续建筑'\n            ],\n            isJoined: true,\n            activity: 'high'\n        },\n        {\n            id: 'ai-creators',\n            name: 'AI创作者',\n            icon: '🤖',\n            members: 1890,\n            category: 'creative',\n            description: 'AI辅助创作和艺术探索',\n            tags: [\n                'AI艺术',\n                'Midjourney',\n                'Stable Diffusion'\n            ],\n            isJoined: true,\n            activity: 'high'\n        },\n        {\n            id: 'web3-builders',\n            name: 'Web3建设者',\n            icon: '🌐',\n            members: 1567,\n            category: 'technology',\n            description: '区块链和Web3技术讨论',\n            tags: [\n                'DeFi',\n                'NFT',\n                'DAO'\n            ],\n            isJoined: false,\n            activity: 'medium'\n        },\n        {\n            id: 'metaverse-designers',\n            name: '元宇宙设计师',\n            icon: '🌌',\n            members: 1234,\n            category: 'design',\n            description: '虚拟世界设计和体验创新',\n            tags: [\n                'VR',\n                'AR',\n                '3D设计'\n            ],\n            isJoined: true,\n            activity: 'high'\n        }\n    ];\n    // NFT市场数据\n    const nftMarketplace = [\n        {\n            id: 'nft-1',\n            title: '未来城市建筑设计',\n            creator: '建筑大师·王设计',\n            price: '0.5 ETH',\n            image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',\n            likes: 234,\n            category: 'architecture',\n            rarity: 'rare',\n            verified: true\n        },\n        {\n            id: 'nft-2',\n            title: 'AI生成艺术作品',\n            creator: 'AI艺术家·小创',\n            price: '0.3 ETH',\n            image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=300&fit=crop',\n            likes: 456,\n            category: 'ai-art',\n            rarity: 'epic',\n            verified: true\n        },\n        {\n            id: 'nft-3',\n            title: '智慧城市概念图',\n            creator: '城市规划师',\n            price: '0.8 ETH',\n            image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=300&h=300&fit=crop',\n            likes: 189,\n            category: 'concept',\n            rarity: 'legendary',\n            verified: true\n        }\n    ];\n    const mockSpaces = [\n        {\n            id: '1',\n            title: '未来建筑师聚会空间',\n            description: '专为建筑师和设计师打造的虚拟聚会空间，支持3D模型展示、实时协作设计和专业交流。',\n            host: {\n                name: '建筑大师·王设计',\n                avatar: '🏛️',\n                verified: true,\n                followers: 15600,\n                reputation: 95\n            },\n            stats: {\n                participants: 234,\n                likes: 1890,\n                comments: 456,\n                shares: 123\n            },\n            tags: [\n                '建筑设计',\n                'VR协作',\n                '专业交流',\n                '3D展示',\n                '设计师社区'\n            ],\n            media: {\n                type: 'vr-space',\n                url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',\n                modelUrl: '/models/architect-space.glb'\n            },\n            spaceType: 'public',\n            capacity: 500,\n            currentUsers: 234,\n            features: [\n                '3D模型展示',\n                '语音聊天',\n                '屏幕共享',\n                'AI助手',\n                '实时协作'\n            ],\n            location: '虚拟建筑学院',\n            startTime: '2025-01-15T19:00:00Z'\n        },\n        {\n            id: '2',\n            title: 'AI创作者元宇宙派对',\n            description: 'AI艺术家和创作者的专属聚会空间，展示最新AI生成艺术作品，交流创作技巧和商业合作。',\n            host: {\n                name: 'AI艺术家·小创',\n                avatar: '🎨',\n                verified: true,\n                followers: 28900,\n                reputation: 88\n            },\n            stats: {\n                participants: 567,\n                likes: 3450,\n                comments: 890,\n                shares: 234\n            },\n            tags: [\n                'AI艺术',\n                '创作者经济',\n                'NFT展示',\n                '商业合作',\n                '技术交流'\n            ],\n            media: {\n                type: 'metaverse-event',\n                url: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',\n                modelUrl: '/models/creator-party.glb'\n            },\n            spaceType: 'event',\n            capacity: 1000,\n            currentUsers: 567,\n            features: [\n                'NFT画廊',\n                '音乐DJ',\n                '互动游戏',\n                '商务洽谈',\n                'AI生成艺术'\n            ],\n            location: '创作者元宇宙中心',\n            startTime: '2025-01-15T20:00:00Z'\n        },\n        {\n            id: '3',\n            title: '工程师技术分享会',\n            description: '全球工程师的技术分享和学习空间，讨论最新技术趋势、开源项目和职业发展。',\n            host: {\n                name: '技术专家·李工程师',\n                avatar: '⚙️',\n                verified: true,\n                followers: 45200,\n                reputation: 92\n            },\n            stats: {\n                participants: 890,\n                likes: 5670,\n                comments: 1234,\n                shares: 456\n            },\n            tags: [\n                '技术分享',\n                '开源项目',\n                '职业发展',\n                '编程技术',\n                '工程师社区'\n            ],\n            media: {\n                type: 'virtual-room',\n                url: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',\n                modelUrl: '/models/tech-meetup.glb'\n            },\n            spaceType: 'public',\n            capacity: 2000,\n            currentUsers: 890,\n            features: [\n                '代码演示',\n                '技术讲座',\n                '项目展示',\n                '招聘信息',\n                '导师指导'\n            ],\n            location: '全球技术中心',\n            startTime: '2025-01-15T21:00:00Z'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocialPage.useEffect\": ()=>{\n            setLoading(true);\n            setTimeout({\n                \"SocialPage.useEffect\": ()=>{\n                    setSpaces(mockSpaces);\n                    setLoading(false);\n                }\n            }[\"SocialPage.useEffect\"], 1000);\n        }\n    }[\"SocialPage.useEffect\"], []);\n    const formatNumber = (num)=>{\n        if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';\n        if (num >= 1000) return (num / 1000).toFixed(1) + 'k';\n        return num.toString();\n    };\n    const getSpaceTypeColor = (type)=>{\n        switch(type){\n            case 'public':\n                return 'bg-green-500';\n            case 'private':\n                return 'bg-red-500';\n            case 'premium':\n                return 'bg-yellow-500';\n            case 'event':\n                return 'bg-purple-500';\n            default:\n                return 'bg-gray-500';\n        }\n    };\n    const getSpaceTypeText = (type)=>{\n        switch(type){\n            case 'public':\n                return '公开';\n            case 'private':\n                return '私密';\n            case 'premium':\n                return '高级';\n            case 'event':\n                return '活动';\n            default:\n                return '未知';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-green-400 to-teal-500 rounded-2xl flex items-center justify-center mb-4 mx-auto animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white font-bold text-2xl\",\n                            children: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-lg mb-2\",\n                        children: \"社交元宇宙\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-sm\",\n                        children: \"连接虚拟空间中...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n            lineNumber: 337,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-10 flex flex-col h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-black/20 backdrop-blur-lg border-b border-white/10 p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-green-400 to-teal-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-sm\",\n                                                children: \"\\uD83D\\uDE80\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: \"社交元宇宙\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-300\",\n                                                    children: \"虚拟空间 • 实时聊天 • 社交网络\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-white/20 rounded-full px-2 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs\",\n                                                children: \"1.2k 在线\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 bg-white/10 rounded-lg p-1 overflow-x-auto\",\n                            children: [\n                                {\n                                    key: 'spaces',\n                                    label: '虚拟空间',\n                                    icon: '🌌'\n                                },\n                                {\n                                    key: 'chat',\n                                    label: '实时聊天',\n                                    icon: '💬'\n                                },\n                                {\n                                    key: 'community',\n                                    label: '社区群组',\n                                    icon: '👥'\n                                },\n                                {\n                                    key: 'friends',\n                                    label: '好友动态',\n                                    icon: '🤝'\n                                },\n                                {\n                                    key: 'nft',\n                                    label: 'NFT市场',\n                                    icon: '🎨'\n                                },\n                                {\n                                    key: 'dao',\n                                    label: 'DAO治理',\n                                    icon: '🏛️'\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.key),\n                                    className: \"flex-shrink-0 flex items-center justify-center space-x-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === tab.key ? 'bg-green-500 text-white' : 'text-white/70 hover:text-white'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tab.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"whitespace-nowrap\",\n                                            children: tab.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, tab.key, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden\",\n                    children: [\n                        activeTab === 'spaces' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: spaces.map((space, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                children: space.host.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: space.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-green-300\",\n                                                                        children: space.host.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 text-xs text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            space.currentUsers,\n                                                                            \"/\",\n                                                                            space.capacity\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 \".concat(getSpaceTypeColor(space.spaceType), \" rounded-full text-xs font-medium mt-1 inline-block\"),\n                                                                children: getSpaceTypeText(space.spaceType)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300 mb-3\",\n                                                children: space.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: space.features.slice(0, 2).map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-green-500/20 rounded text-xs\",\n                                                                children: feature\n                                                            }, idx, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"px-4 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium\",\n                                                        children: \"\\uD83D\\uDE80 进入空间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 19\n                                    }, this)\n                                }, space.id, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-b border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 overflow-x-auto\",\n                                        children: chatRooms.map((room)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedChatRoom(room.id),\n                                                className: \"flex-shrink-0 flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors \".concat(selectedChatRoom === room.id ? 'bg-green-500 border-green-500 text-white' : 'bg-white/10 border-white/20 text-gray-300 hover:border-white/40'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: room.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: room.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-white/20 px-1 rounded\",\n                                                        children: room.members\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, room.id, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto p-4 space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl mb-2\",\n                                                    children: (_chatRooms_find = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find === void 0 ? void 0 : _chatRooms_find.icon\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (_chatRooms_find1 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find1 === void 0 ? void 0 : _chatRooms_find1.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: (_chatRooms_find2 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find2 === void 0 ? void 0 : _chatRooms_find2.description\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-400 mt-1\",\n                                                    children: [\n                                                        (_chatRooms_find3 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find3 === void 0 ? void 0 : _chatRooms_find3.members,\n                                                        \" 成员在线\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this),\n                                        [\n                                            {\n                                                id: 1,\n                                                user: '🏗️ 建筑师小王',\n                                                message: '刚完成了一个AI辅助的建筑设计，效果很棒！',\n                                                time: '2分钟前',\n                                                isMe: false,\n                                                avatar: '🏗️'\n                                            },\n                                            {\n                                                id: 2,\n                                                user: '我',\n                                                message: '能分享一下设计图吗？很想看看AI的效果',\n                                                time: '1分钟前',\n                                                isMe: true,\n                                                avatar: '👨‍💻'\n                                            },\n                                            {\n                                                id: 3,\n                                                user: '🎨 AI艺术家',\n                                                message: '我也在用AI创作NFT，最近很火呢',\n                                                time: '30秒前',\n                                                isMe: false,\n                                                avatar: '🎨'\n                                            },\n                                            {\n                                                id: 4,\n                                                user: '🌐 Web3开发者',\n                                                message: '有人想合作开发DApp吗？',\n                                                time: '刚刚',\n                                                isMe: false,\n                                                avatar: '🌐'\n                                            }\n                                        ].map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex \".concat(msg.isMe ? 'justify-end' : 'justify-start'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-w-[80%] flex \".concat(msg.isMe ? 'flex-row-reverse' : 'flex-row', \" items-start space-x-2\"),\n                                                    children: [\n                                                        !msg.isMe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-sm\",\n                                                            children: msg.avatar\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\".concat(msg.isMe ? 'bg-green-500 mr-2' : 'bg-white/10', \" rounded-lg p-3\"),\n                                                            children: [\n                                                                !msg.isMe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-300 mb-1\",\n                                                                    children: msg.user\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 39\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-white\",\n                                                                    children: msg.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400 mt-1\",\n                                                                    children: msg.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, msg.id, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 pb-20 border-t border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-gray-400 hover:text-white\",\n                                                children: \"\\uD83D\\uDCCE\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newMessage,\n                                                onChange: (e)=>setNewMessage(e.target.value),\n                                                placeholder: \"在 \".concat((_chatRooms_find4 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find4 === void 0 ? void 0 : _chatRooms_find4.name, \" 中发消息...\"),\n                                                className: \"flex-1 bg-white/10 text-white px-4 py-3 rounded-full border border-white/20 focus:border-green-500 focus:outline-none\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-gray-400 hover:text-white\",\n                                                children: \"\\uD83D\\uDE0A\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl\",\n                                                    children: \"\\uD83D\\uDE80\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'community' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"⭐\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"推荐群组\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: communityGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                            children: group.icon\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-bold text-white\",\n                                                                                    children: group.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                    lineNumber: 554,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-green-300\",\n                                                                                    children: [\n                                                                                        group.members.toLocaleString(),\n                                                                                        \" 成员\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                    lineNumber: 555,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 rounded-full \".concat(group.activity === 'high' ? 'bg-green-400' : group.activity === 'medium' ? 'bg-yellow-400' : 'bg-gray-400')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: group.activity === 'high' ? '活跃' : group.activity === 'medium' ? '一般' : '较少'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-300 mb-3\",\n                                                            children: group.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                                            children: group.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-green-500/20 rounded text-xs text-green-300\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        tag\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400 capitalize\",\n                                                                    children: group.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(group.isJoined ? 'bg-gray-600 text-gray-300 hover:bg-gray-500' : 'bg-gradient-to-r from-green-500 to-teal-500 text-white hover:from-green-600 hover:to-teal-600'),\n                                                                    children: group.isJoined ? '已加入' : '加入群组'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, group.id, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDC65\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"我的群组\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: communityGroups.filter((g)=>g.isJoined).map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center\",\n                                                                        children: group.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 608,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-white\",\n                                                                                children: group.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 612,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: [\n                                                                                    group.members.toLocaleString(),\n                                                                                    \" 成员\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 613,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"px-3 py-1 bg-green-500 rounded-lg text-xs font-medium\",\n                                                                        children: \"进入\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 617,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium text-gray-400\",\n                                                                        children: \"设置\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, group.id, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"➕\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"创建群组\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300 mb-4\",\n                                            children: \"创建专属的专业群组，聚集志同道合的伙伴，共同探讨技术和创意。\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-white font-medium\",\n                                            children: \"\\uD83D\\uDE80 创建新群组\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'friends' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                {\n                                    id: 1,\n                                    user: '🏗️ 建筑师小王',\n                                    action: '发布了新的BIM模型',\n                                    content: 'AI驱动的智慧建筑设计',\n                                    time: '5分钟前',\n                                    likes: 23\n                                },\n                                {\n                                    id: 2,\n                                    user: '🎨 AI艺术家',\n                                    action: '创建了NFT作品',\n                                    content: '赛博朋克风格的未来城市',\n                                    time: '15分钟前',\n                                    likes: 45\n                                },\n                                {\n                                    id: 3,\n                                    user: '🚀 元宇宙设计师',\n                                    action: '加入了虚拟空间',\n                                    content: '建筑师专业交流会',\n                                    time: '30分钟前',\n                                    likes: 12\n                                }\n                            ].map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: activity.user.split(' ')[0]\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: activity.user.split(' ')[1]\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: [\n                                                                    \" \",\n                                                                    activity.action\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-300 mt-1\",\n                                                        children: activity.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 mt-2 text-xs text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: activity.time\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"flex items-center space-x-1 text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"❤️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: activity.likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"\\uD83D\\uDCAC 回复\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 19\n                                    }, this)\n                                }, activity.id, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'dao' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83C\\uDFDB️\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"NextGen DAO\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-400\",\n                                                            children: \"25,680\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-green-200\",\n                                                            children: \"NGT持有者\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-400\",\n                                                            children: \"156\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-blue-200\",\n                                                            children: \"活跃提案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-400\",\n                                                            children: \"89%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: \"参与率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-yellow-400\",\n                                                            children: \"4.8M\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 708,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-yellow-200\",\n                                                            children: \"总投票权\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/5 rounded-lg p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300\",\n                                                children: \"NextGen DAO是一个去中心化自治组织，由社区成员共同治理平台的发展方向。 持有NGT代币即可参与提案投票，共同决定平台的未来。\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"活跃提案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                {\n                                                    id: 'prop-042',\n                                                    title: '降低平台交易手续费至2%',\n                                                    description: '建议将NFT交易手续费从3%降低至2%，以提高平台竞争力',\n                                                    status: 'voting',\n                                                    votes: {\n                                                        for: 15420,\n                                                        against: 3280\n                                                    },\n                                                    endTime: '2025-01-20',\n                                                    category: 'economic'\n                                                },\n                                                {\n                                                    id: 'prop-043',\n                                                    title: '新增VR虚拟展厅功能',\n                                                    description: '为创作者提供VR虚拟展厅，展示NFT作品集',\n                                                    status: 'voting',\n                                                    votes: {\n                                                        for: 12890,\n                                                        against: 1560\n                                                    },\n                                                    endTime: '2025-01-22',\n                                                    category: 'feature'\n                                                },\n                                                {\n                                                    id: 'prop-044',\n                                                    title: '建立创作者扶持基金',\n                                                    description: '从平台收益中拨出10%建立创作者扶持基金',\n                                                    status: 'passed',\n                                                    votes: {\n                                                        for: 18920,\n                                                        against: 2340\n                                                    },\n                                                    endTime: '2025-01-15',\n                                                    category: 'community'\n                                                }\n                                            ].map((proposal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: proposal.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(proposal.status === 'voting' ? 'bg-yellow-500' : proposal.status === 'passed' ? 'bg-green-500' : 'bg-red-500'),\n                                                                    children: proposal.status === 'voting' ? '投票中' : proposal.status === 'passed' ? '已通过' : '未通过'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 756,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-300 mb-3\",\n                                                            children: proposal.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-xs mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-400\",\n                                                                            children: [\n                                                                                \"赞成: \",\n                                                                                proposal.votes.for.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 770,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: [\n                                                                                \"反对: \",\n                                                                                proposal.votes.against.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 771,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-white/20 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full\",\n                                                                        style: {\n                                                                            width: \"\".concat(proposal.votes.for / (proposal.votes.for + proposal.votes.against) * 100, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 774,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: proposal.status === 'voting' ? \"截止: \".concat(proposal.endTime) : \"结束: \".concat(proposal.endTime)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                proposal.status === 'voting' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"px-3 py-1 bg-green-500 rounded-lg text-xs font-medium\",\n                                                                            children: \"赞成\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"px-3 py-1 bg-red-500 rounded-lg text-xs font-medium\",\n                                                                            children: \"反对\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 792,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, proposal.id, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"专业委员会\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3\",\n                                            children: [\n                                                {\n                                                    name: '技术委员会',\n                                                    icon: '⚙️',\n                                                    members: 12,\n                                                    description: '技术路线制定'\n                                                },\n                                                {\n                                                    name: '内容委员会',\n                                                    icon: '📝',\n                                                    members: 8,\n                                                    description: '内容质量监管'\n                                                },\n                                                {\n                                                    name: '经济委员会',\n                                                    icon: '💰',\n                                                    members: 10,\n                                                    description: '代币经济设计'\n                                                },\n                                                {\n                                                    name: '仲裁委员会',\n                                                    icon: '⚖️',\n                                                    members: 6,\n                                                    description: '争议处理'\n                                                }\n                                            ].map((committee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: committee.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-white text-sm\",\n                                                                    children: committee.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 816,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mb-2\",\n                                                            children: committee.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 818,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-400\",\n                                                                    children: [\n                                                                        committee.members,\n                                                                        \" 成员\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-xs text-blue-400 hover:text-blue-300\",\n                                                                    children: \"查看详情\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, committee.name, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 804,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"我的治理参与\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-purple-400\",\n                                                            children: \"1,250\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: \"投票权重\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 834,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 832,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-green-400\",\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-green-200\",\n                                                            children: \"参与提案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 838,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-yellow-400\",\n                                                            children: \"89%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 841,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-yellow-200\",\n                                                            children: \"参与率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 842,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 831,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium\",\n                                                    children: \"创建提案\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-sm font-medium\",\n                                                    children: \"委托投票\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 829,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n            lineNumber: 352,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n        lineNumber: 350,\n        columnNumber: 5\n    }, this);\n}\n_s(SocialPage, \"oYvQcbLGgnoxv9j60RjZreD36jU=\");\n_c = SocialPage;\nvar _c;\n$RefreshReg$(_c, \"SocialPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/social/page.tsx\n"));

/***/ })

});