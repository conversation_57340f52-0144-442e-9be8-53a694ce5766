"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/social/page",{

/***/ "(app-pages-browser)/./src/app/social/page.tsx":
/*!*********************************!*\
  !*** ./src/app/social/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SocialPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SocialPage() {\n    _s();\n    const [spaces, setSpaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('spaces');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedChatRoom, setSelectedChatRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [showCreateSpace, setShowCreateSpace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProfile, setShowProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [onlineUsers, setOnlineUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '工程师·张三',\n        avatar: '👨‍💻',\n        level: 5,\n        ngtBalance: 1250,\n        reputation: 95,\n        followers: 2340,\n        following: 890,\n        verified: true,\n        did: 'did:ngt:0x1234...5678'\n    });\n    const mockSpaces = [\n        {\n            id: '1',\n            title: '未来建筑师聚会空间',\n            description: '专为建筑师和设计师打造的虚拟聚会空间，支持3D模型展示、实时协作设计和专业交流。',\n            host: {\n                name: '建筑大师·王设计',\n                avatar: '🏛️',\n                verified: true,\n                followers: 15600,\n                reputation: 95\n            },\n            stats: {\n                participants: 234,\n                likes: 1890,\n                comments: 456,\n                shares: 123\n            },\n            tags: [\n                '建筑设计',\n                'VR协作',\n                '专业交流',\n                '3D展示',\n                '设计师社区'\n            ],\n            media: {\n                type: 'vr-space',\n                url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',\n                modelUrl: '/models/architect-space.glb'\n            },\n            spaceType: 'public',\n            capacity: 500,\n            currentUsers: 234,\n            features: [\n                '3D模型展示',\n                '语音聊天',\n                '屏幕共享',\n                'AI助手',\n                '实时协作'\n            ],\n            location: '虚拟建筑学院',\n            startTime: '2025-01-15T19:00:00Z'\n        },\n        {\n            id: '2',\n            title: 'AI创作者元宇宙派对',\n            description: 'AI艺术家和创作者的专属聚会空间，展示最新AI生成艺术作品，交流创作技巧和商业合作。',\n            host: {\n                name: 'AI艺术家·小创',\n                avatar: '🎨',\n                verified: true,\n                followers: 28900,\n                reputation: 88\n            },\n            stats: {\n                participants: 567,\n                likes: 3450,\n                comments: 890,\n                shares: 234\n            },\n            tags: [\n                'AI艺术',\n                '创作者经济',\n                'NFT展示',\n                '商业合作',\n                '技术交流'\n            ],\n            media: {\n                type: 'metaverse-event',\n                url: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',\n                modelUrl: '/models/creator-party.glb'\n            },\n            spaceType: 'event',\n            capacity: 1000,\n            currentUsers: 567,\n            features: [\n                'NFT画廊',\n                '音乐DJ',\n                '互动游戏',\n                '商务洽谈',\n                'AI生成艺术'\n            ],\n            location: '创作者元宇宙中心',\n            startTime: '2025-01-15T20:00:00Z'\n        },\n        {\n            id: '3',\n            title: '工程师技术分享会',\n            description: '全球工程师的技术分享和学习空间，讨论最新技术趋势、开源项目和职业发展。',\n            host: {\n                name: '技术专家·李工程师',\n                avatar: '⚙️',\n                verified: true,\n                followers: 45200,\n                reputation: 92\n            },\n            stats: {\n                participants: 890,\n                likes: 5670,\n                comments: 1234,\n                shares: 456\n            },\n            tags: [\n                '技术分享',\n                '开源项目',\n                '职业发展',\n                '编程技术',\n                '工程师社区'\n            ],\n            media: {\n                type: 'virtual-room',\n                url: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',\n                modelUrl: '/models/tech-meetup.glb'\n            },\n            spaceType: 'public',\n            capacity: 2000,\n            currentUsers: 890,\n            features: [\n                '代码演示',\n                '技术讲座',\n                '项目展示',\n                '招聘信息',\n                '导师指导'\n            ],\n            location: '全球技术中心',\n            startTime: '2025-01-15T21:00:00Z'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocialPage.useEffect\": ()=>{\n            setLoading(true);\n            setTimeout({\n                \"SocialPage.useEffect\": ()=>{\n                    setSpaces(mockSpaces);\n                    setLoading(false);\n                }\n            }[\"SocialPage.useEffect\"], 1000);\n        }\n    }[\"SocialPage.useEffect\"], []);\n    const formatNumber = (num)=>{\n        if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';\n        if (num >= 1000) return (num / 1000).toFixed(1) + 'k';\n        return num.toString();\n    };\n    const getSpaceTypeColor = (type)=>{\n        switch(type){\n            case 'public':\n                return 'bg-green-500';\n            case 'private':\n                return 'bg-red-500';\n            case 'premium':\n                return 'bg-yellow-500';\n            case 'event':\n                return 'bg-purple-500';\n            default:\n                return 'bg-gray-500';\n        }\n    };\n    const getSpaceTypeText = (type)=>{\n        switch(type){\n            case 'public':\n                return '公开';\n            case 'private':\n                return '私密';\n            case 'premium':\n                return '高级';\n            case 'event':\n                return '活动';\n            default:\n                return '未知';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-green-400 to-teal-500 rounded-2xl flex items-center justify-center mb-4 mx-auto animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white font-bold text-2xl\",\n                            children: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-lg mb-2\",\n                        children: \"社交元宇宙\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-sm\",\n                        children: \"连接虚拟空间中...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-10 flex flex-col h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-black/20 backdrop-blur-lg border-b border-white/10 p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-green-400 to-teal-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-sm\",\n                                                children: \"\\uD83D\\uDE80\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: \"社交元宇宙\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-300\",\n                                                    children: \"虚拟空间 • 实时聊天 • 社交网络\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-white/20 rounded-full px-2 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs\",\n                                                children: \"1.2k 在线\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 bg-white/10 rounded-lg p-1 overflow-x-auto\",\n                            children: [\n                                {\n                                    key: 'spaces',\n                                    label: '虚拟空间',\n                                    icon: '🌌'\n                                },\n                                {\n                                    key: 'chat',\n                                    label: '实时聊天',\n                                    icon: '💬'\n                                },\n                                {\n                                    key: 'friends',\n                                    label: '好友动态',\n                                    icon: '👥'\n                                },\n                                {\n                                    key: 'dao',\n                                    label: 'DAO治理',\n                                    icon: '🏛️'\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.key),\n                                    className: \"flex-shrink-0 flex items-center justify-center space-x-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === tab.key ? 'bg-green-500 text-white' : 'text-white/70 hover:text-white'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tab.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"whitespace-nowrap\",\n                                            children: tab.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, tab.key, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden\",\n                    children: [\n                        activeTab === 'spaces' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: spaces.map((space, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                children: space.host.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: space.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-green-300\",\n                                                                        children: space.host.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 text-xs text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            space.currentUsers,\n                                                                            \"/\",\n                                                                            space.capacity\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 \".concat(getSpaceTypeColor(space.spaceType), \" rounded-full text-xs font-medium mt-1 inline-block\"),\n                                                                children: getSpaceTypeText(space.spaceType)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300 mb-3\",\n                                                children: space.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: space.features.slice(0, 2).map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-green-500/20 rounded text-xs\",\n                                                                children: feature\n                                                            }, idx, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"px-4 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium\",\n                                                        children: \"\\uD83D\\uDE80 进入空间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 19\n                                    }, this)\n                                }, space.id, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto p-4 space-y-3\",\n                                    children: [\n                                        {\n                                            id: 1,\n                                            user: '🏗️ 建筑师小王',\n                                            message: '刚完成了一个AI辅助的建筑设计，效果很棒！',\n                                            time: '2分钟前',\n                                            isMe: false\n                                        },\n                                        {\n                                            id: 2,\n                                            user: '我',\n                                            message: '能分享一下设计图吗？很想看看AI的效果',\n                                            time: '1分钟前',\n                                            isMe: true\n                                        },\n                                        {\n                                            id: 3,\n                                            user: '🎨 AI艺术家',\n                                            message: '我也在用AI创作NFT，最近很火呢',\n                                            time: '30秒前',\n                                            isMe: false\n                                        }\n                                    ].map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex \".concat(msg.isMe ? 'justify-end' : 'justify-start'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[80%] \".concat(msg.isMe ? 'bg-green-500' : 'bg-white/10', \" rounded-lg p-3\"),\n                                                children: [\n                                                    !msg.isMe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-300 mb-1\",\n                                                        children: msg.user\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-white\",\n                                                        children: msg.message\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: msg.time\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, msg.id, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 pb-20 border-t border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newMessage,\n                                                onChange: (e)=>setNewMessage(e.target.value),\n                                                placeholder: \"输入消息...\",\n                                                className: \"flex-1 bg-white/10 text-white px-4 py-3 rounded-full border border-white/20 focus:border-green-500 focus:outline-none\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl\",\n                                                    children: \"\\uD83D\\uDE80\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'friends' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                {\n                                    id: 1,\n                                    user: '🏗️ 建筑师小王',\n                                    action: '发布了新的BIM模型',\n                                    content: 'AI驱动的智慧建筑设计',\n                                    time: '5分钟前',\n                                    likes: 23\n                                },\n                                {\n                                    id: 2,\n                                    user: '🎨 AI艺术家',\n                                    action: '创建了NFT作品',\n                                    content: '赛博朋克风格的未来城市',\n                                    time: '15分钟前',\n                                    likes: 45\n                                },\n                                {\n                                    id: 3,\n                                    user: '🚀 元宇宙设计师',\n                                    action: '加入了虚拟空间',\n                                    content: '建筑师专业交流会',\n                                    time: '30分钟前',\n                                    likes: 12\n                                }\n                            ].map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: activity.user.split(' ')[0]\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: activity.user.split(' ')[1]\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: [\n                                                                    \" \",\n                                                                    activity.action\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-300 mt-1\",\n                                                        children: activity.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 mt-2 text-xs text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: activity.time\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"flex items-center space-x-1 text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"❤️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: activity.likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"\\uD83D\\uDCAC 回复\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 19\n                                    }, this)\n                                }, activity.id, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'dao' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83C\\uDFDB️\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"NextGen DAO\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-400\",\n                                                            children: \"25,680\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-green-200\",\n                                                            children: \"NGT持有者\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-400\",\n                                                            children: \"156\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-blue-200\",\n                                                            children: \"活跃提案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-400\",\n                                                            children: \"89%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: \"参与率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-yellow-400\",\n                                                            children: \"4.8M\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-yellow-200\",\n                                                            children: \"总投票权\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/5 rounded-lg p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300\",\n                                                children: \"NextGen DAO是一个去中心化自治组织，由社区成员共同治理平台的发展方向。 持有NGT代币即可参与提案投票，共同决定平台的未来。\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"活跃提案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                {\n                                                    id: 'prop-042',\n                                                    title: '降低平台交易手续费至2%',\n                                                    description: '建议将NFT交易手续费从3%降低至2%，以提高平台竞争力',\n                                                    status: 'voting',\n                                                    votes: {\n                                                        for: 15420,\n                                                        against: 3280\n                                                    },\n                                                    endTime: '2025-01-20',\n                                                    category: 'economic'\n                                                },\n                                                {\n                                                    id: 'prop-043',\n                                                    title: '新增VR虚拟展厅功能',\n                                                    description: '为创作者提供VR虚拟展厅，展示NFT作品集',\n                                                    status: 'voting',\n                                                    votes: {\n                                                        for: 12890,\n                                                        against: 1560\n                                                    },\n                                                    endTime: '2025-01-22',\n                                                    category: 'feature'\n                                                },\n                                                {\n                                                    id: 'prop-044',\n                                                    title: '建立创作者扶持基金',\n                                                    description: '从平台收益中拨出10%建立创作者扶持基金',\n                                                    status: 'passed',\n                                                    votes: {\n                                                        for: 18920,\n                                                        against: 2340\n                                                    },\n                                                    endTime: '2025-01-15',\n                                                    category: 'community'\n                                                }\n                                            ].map((proposal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: proposal.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(proposal.status === 'voting' ? 'bg-yellow-500' : proposal.status === 'passed' ? 'bg-green-500' : 'bg-red-500'),\n                                                                    children: proposal.status === 'voting' ? '投票中' : proposal.status === 'passed' ? '已通过' : '未通过'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-300 mb-3\",\n                                                            children: proposal.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-xs mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-400\",\n                                                                            children: [\n                                                                                \"赞成: \",\n                                                                                proposal.votes.for.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: [\n                                                                                \"反对: \",\n                                                                                proposal.votes.against.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-white/20 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full\",\n                                                                        style: {\n                                                                            width: \"\".concat(proposal.votes.for / (proposal.votes.for + proposal.votes.against) * 100, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: proposal.status === 'voting' ? \"截止: \".concat(proposal.endTime) : \"结束: \".concat(proposal.endTime)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                proposal.status === 'voting' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"px-3 py-1 bg-green-500 rounded-lg text-xs font-medium\",\n                                                                            children: \"赞成\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"px-3 py-1 bg-red-500 rounded-lg text-xs font-medium\",\n                                                                            children: \"反对\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, proposal.id, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"专业委员会\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3\",\n                                            children: [\n                                                {\n                                                    name: '技术委员会',\n                                                    icon: '⚙️',\n                                                    members: 12,\n                                                    description: '技术路线制定'\n                                                },\n                                                {\n                                                    name: '内容委员会',\n                                                    icon: '📝',\n                                                    members: 8,\n                                                    description: '内容质量监管'\n                                                },\n                                                {\n                                                    name: '经济委员会',\n                                                    icon: '💰',\n                                                    members: 10,\n                                                    description: '代币经济设计'\n                                                },\n                                                {\n                                                    name: '仲裁委员会',\n                                                    icon: '⚖️',\n                                                    members: 6,\n                                                    description: '争议处理'\n                                                }\n                                            ].map((committee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: committee.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-white text-sm\",\n                                                                    children: committee.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mb-2\",\n                                                            children: committee.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-400\",\n                                                                    children: [\n                                                                        committee.members,\n                                                                        \" 成员\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-xs text-blue-400 hover:text-blue-300\",\n                                                                    children: \"查看详情\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, committee.name, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"我的治理参与\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-purple-400\",\n                                                            children: \"1,250\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: \"投票权重\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-green-400\",\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-green-200\",\n                                                            children: \"参与提案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-yellow-400\",\n                                                            children: \"89%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-yellow-200\",\n                                                            children: \"参与率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium\",\n                                                    children: \"创建提案\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-sm font-medium\",\n                                                    children: \"委托投票\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s(SocialPage, \"oYvQcbLGgnoxv9j60RjZreD36jU=\");\n_c = SocialPage;\nvar _c;\n$RefreshReg$(_c, \"SocialPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/social/page.tsx\n"));

/***/ })

});