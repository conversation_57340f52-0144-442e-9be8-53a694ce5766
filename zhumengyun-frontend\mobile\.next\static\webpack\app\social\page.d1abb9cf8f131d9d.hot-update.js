"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/social/page",{

/***/ "(app-pages-browser)/./src/app/social/page.tsx":
/*!*********************************!*\
  !*** ./src/app/social/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SocialPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SocialPage() {\n    var _chatRooms_find, _chatRooms_find1, _chatRooms_find2, _chatRooms_find3, _chatRooms_find4;\n    _s();\n    const [spaces, setSpaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('spaces');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedChatRoom, setSelectedChatRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [showCreateSpace, setShowCreateSpace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProfile, setShowProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [onlineUsers, setOnlineUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '工程师·张三',\n        avatar: '👨‍💻',\n        level: 5,\n        ngtBalance: 1250,\n        reputation: 95,\n        followers: 2340,\n        following: 890,\n        verified: true,\n        did: 'did:ngt:0x1234...5678'\n    });\n    // 弹窗状态\n    const [showEnterSpace, setShowEnterSpace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFileUpload, setShowFileUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateGroup, setShowCreateGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBuyNFT, setShowBuyNFT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateNFT, setShowCreateNFT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateProposal, setShowCreateProposal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDelegateVote, setShowDelegateVote] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommitteeDetail, setShowCommitteeDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccessToast, setShowSuccessToast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toastMessage, setToastMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 选中的项目\n    const [selectedSpace, setSelectedSpace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedNFT, setSelectedNFT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCommittee, setSelectedCommittee] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProposal, setSelectedProposal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 加载状态\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sendingMessage, setSendingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 聊天室数据\n    const chatRooms = [\n        {\n            id: 'general',\n            name: '综合讨论',\n            icon: '💬',\n            members: 1234,\n            description: '工程师和创作者的综合交流空间',\n            type: 'public',\n            lastMessage: {\n                user: '建筑师小王',\n                content: '刚完成了一个AI辅助的建筑设计',\n                time: '2分钟前'\n            }\n        },\n        {\n            id: 'tech',\n            name: '技术讨论',\n            icon: '⚙️',\n            members: 890,\n            description: '技术分享和问题讨论',\n            type: 'public',\n            lastMessage: {\n                user: 'AI专家',\n                content: '最新的GPT模型在工程设计中的应用',\n                time: '5分钟前'\n            }\n        },\n        {\n            id: 'nft',\n            name: 'NFT创作',\n            icon: '🎨',\n            members: 567,\n            description: 'NFT创作和交易讨论',\n            type: 'public',\n            lastMessage: {\n                user: '数字艺术家',\n                content: '分享一个新的NFT作品',\n                time: '10分钟前'\n            }\n        },\n        {\n            id: 'dao',\n            name: 'DAO治理',\n            icon: '🏛️',\n            members: 456,\n            description: '平台治理和提案讨论',\n            type: 'premium',\n            lastMessage: {\n                user: '社区管理员',\n                content: '新提案：降低交易手续费',\n                time: '15分钟前'\n            }\n        }\n    ];\n    // 社区群组数据\n    const communityGroups = [\n        {\n            id: 'architects',\n            name: '建筑师联盟',\n            icon: '🏗️',\n            members: 2340,\n            category: 'professional',\n            description: '全球建筑师专业交流社区',\n            tags: [\n                'BIM',\n                'AI设计',\n                '可持续建筑'\n            ],\n            isJoined: true,\n            activity: 'high'\n        },\n        {\n            id: 'ai-creators',\n            name: 'AI创作者',\n            icon: '🤖',\n            members: 1890,\n            category: 'creative',\n            description: 'AI辅助创作和艺术探索',\n            tags: [\n                'AI艺术',\n                'Midjourney',\n                'Stable Diffusion'\n            ],\n            isJoined: true,\n            activity: 'high'\n        },\n        {\n            id: 'web3-builders',\n            name: 'Web3建设者',\n            icon: '🌐',\n            members: 1567,\n            category: 'technology',\n            description: '区块链和Web3技术讨论',\n            tags: [\n                'DeFi',\n                'NFT',\n                'DAO'\n            ],\n            isJoined: false,\n            activity: 'medium'\n        },\n        {\n            id: 'metaverse-designers',\n            name: '元宇宙设计师',\n            icon: '🌌',\n            members: 1234,\n            category: 'design',\n            description: '虚拟世界设计和体验创新',\n            tags: [\n                'VR',\n                'AR',\n                '3D设计'\n            ],\n            isJoined: true,\n            activity: 'high'\n        }\n    ];\n    // NFT市场数据\n    const nftMarketplace = [\n        {\n            id: 'nft-1',\n            title: '未来城市建筑设计',\n            creator: '建筑大师·王设计',\n            price: '0.5 ETH',\n            image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',\n            likes: 234,\n            category: 'architecture',\n            rarity: 'rare',\n            verified: true\n        },\n        {\n            id: 'nft-2',\n            title: 'AI生成艺术作品',\n            creator: 'AI艺术家·小创',\n            price: '0.3 ETH',\n            image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=300&fit=crop',\n            likes: 456,\n            category: 'ai-art',\n            rarity: 'epic',\n            verified: true\n        },\n        {\n            id: 'nft-3',\n            title: '智慧城市概念图',\n            creator: '城市规划师',\n            price: '0.8 ETH',\n            image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=300&h=300&fit=crop',\n            likes: 189,\n            category: 'concept',\n            rarity: 'legendary',\n            verified: true\n        }\n    ];\n    const mockSpaces = [\n        {\n            id: '1',\n            title: '未来建筑师聚会空间',\n            description: '专为建筑师和设计师打造的虚拟聚会空间，支持3D模型展示、实时协作设计和专业交流。',\n            host: {\n                name: '建筑大师·王设计',\n                avatar: '🏛️',\n                verified: true,\n                followers: 15600,\n                reputation: 95\n            },\n            stats: {\n                participants: 234,\n                likes: 1890,\n                comments: 456,\n                shares: 123\n            },\n            tags: [\n                '建筑设计',\n                'VR协作',\n                '专业交流',\n                '3D展示',\n                '设计师社区'\n            ],\n            media: {\n                type: 'vr-space',\n                url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',\n                modelUrl: '/models/architect-space.glb'\n            },\n            spaceType: 'public',\n            capacity: 500,\n            currentUsers: 234,\n            features: [\n                '3D模型展示',\n                '语音聊天',\n                '屏幕共享',\n                'AI助手',\n                '实时协作'\n            ],\n            location: '虚拟建筑学院',\n            startTime: '2025-01-15T19:00:00Z'\n        },\n        {\n            id: '2',\n            title: 'AI创作者元宇宙派对',\n            description: 'AI艺术家和创作者的专属聚会空间，展示最新AI生成艺术作品，交流创作技巧和商业合作。',\n            host: {\n                name: 'AI艺术家·小创',\n                avatar: '🎨',\n                verified: true,\n                followers: 28900,\n                reputation: 88\n            },\n            stats: {\n                participants: 567,\n                likes: 3450,\n                comments: 890,\n                shares: 234\n            },\n            tags: [\n                'AI艺术',\n                '创作者经济',\n                'NFT展示',\n                '商业合作',\n                '技术交流'\n            ],\n            media: {\n                type: 'metaverse-event',\n                url: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',\n                modelUrl: '/models/creator-party.glb'\n            },\n            spaceType: 'event',\n            capacity: 1000,\n            currentUsers: 567,\n            features: [\n                'NFT画廊',\n                '音乐DJ',\n                '互动游戏',\n                '商务洽谈',\n                'AI生成艺术'\n            ],\n            location: '创作者元宇宙中心',\n            startTime: '2025-01-15T20:00:00Z'\n        },\n        {\n            id: '3',\n            title: '工程师技术分享会',\n            description: '全球工程师的技术分享和学习空间，讨论最新技术趋势、开源项目和职业发展。',\n            host: {\n                name: '技术专家·李工程师',\n                avatar: '⚙️',\n                verified: true,\n                followers: 45200,\n                reputation: 92\n            },\n            stats: {\n                participants: 890,\n                likes: 5670,\n                comments: 1234,\n                shares: 456\n            },\n            tags: [\n                '技术分享',\n                '开源项目',\n                '职业发展',\n                '编程技术',\n                '工程师社区'\n            ],\n            media: {\n                type: 'virtual-room',\n                url: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',\n                modelUrl: '/models/tech-meetup.glb'\n            },\n            spaceType: 'public',\n            capacity: 2000,\n            currentUsers: 890,\n            features: [\n                '代码演示',\n                '技术讲座',\n                '项目展示',\n                '招聘信息',\n                '导师指导'\n            ],\n            location: '全球技术中心',\n            startTime: '2025-01-15T21:00:00Z'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocialPage.useEffect\": ()=>{\n            setLoading(true);\n            setTimeout({\n                \"SocialPage.useEffect\": ()=>{\n                    setSpaces(mockSpaces);\n                    setLoading(false);\n                }\n            }[\"SocialPage.useEffect\"], 1000);\n        }\n    }[\"SocialPage.useEffect\"], []);\n    // 显示成功提示\n    const showToast = (message)=>{\n        setToastMessage(message);\n        setShowSuccessToast(true);\n        setTimeout(()=>setShowSuccessToast(false), 3000);\n    };\n    // 进入虚拟空间\n    const handleEnterSpace = (space)=>{\n        setSelectedSpace(space);\n        setShowEnterSpace(true);\n    };\n    const confirmEnterSpace = ()=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowEnterSpace(false);\n            showToast(\"成功进入 \".concat(selectedSpace === null || selectedSpace === void 0 ? void 0 : selectedSpace.title));\n        }, 2000);\n    };\n    // 发送消息\n    const handleSendMessage = ()=>{\n        if (!newMessage.trim()) return;\n        setSendingMessage(true);\n        setTimeout(()=>{\n            setSendingMessage(false);\n            showToast('消息发送成功');\n            setNewMessage('');\n        }, 1000);\n    };\n    // 文件上传\n    const handleFileUpload = ()=>{\n        setShowFileUpload(true);\n    };\n    const confirmFileUpload = (files)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowFileUpload(false);\n            showToast(\"成功上传 \".concat(files.length, \" 个文件\"));\n        }, 2000);\n    };\n    // 表情选择\n    const handleEmojiSelect = (emoji)=>{\n        setNewMessage((prev)=>prev + emoji);\n        setShowEmojiPicker(false);\n    };\n    // 群组操作\n    const handleJoinGroup = (groupId)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            showToast('成功加入群组');\n        }, 1500);\n    };\n    const handleLeaveGroup = (groupId)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            showToast('已退出群组');\n        }, 1500);\n    };\n    const handleEnterGroup = (groupId)=>{\n        showToast('正在进入群组聊天...');\n    };\n    const handleGroupSettings = (groupId)=>{\n        showToast('群组设置功能开发中...');\n    };\n    const handleCreateGroup = ()=>{\n        setShowCreateGroup(true);\n    };\n    const confirmCreateGroup = (groupData)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowCreateGroup(false);\n            showToast('群组创建成功');\n        }, 2000);\n    };\n    // NFT操作\n    const handleBuyNFT = (nft)=>{\n        setSelectedNFT(nft);\n        setShowBuyNFT(true);\n    };\n    const confirmBuyNFT = ()=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowBuyNFT(false);\n            showToast(\"成功购买 \".concat(selectedNFT === null || selectedNFT === void 0 ? void 0 : selectedNFT.title));\n        }, 2000);\n    };\n    const handleCreateNFT = ()=>{\n        setShowCreateNFT(true);\n    };\n    const confirmCreateNFT = (nftData)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowCreateNFT(false);\n            showToast('NFT创建成功');\n        }, 3000);\n    };\n    const handleNFTCategory = (category)=>{\n        showToast(\"正在浏览 \".concat(category, \" 分类...\"));\n    };\n    // DAO治理操作\n    const handleVote = (proposalId, vote)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            showToast(\"投票成功：\".concat(vote === 'for' ? '赞成' : '反对'));\n        }, 1500);\n    };\n    const handleCreateProposal = ()=>{\n        setShowCreateProposal(true);\n    };\n    const confirmCreateProposal = (proposalData)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowCreateProposal(false);\n            showToast('提案创建成功');\n        }, 2000);\n    };\n    const handleDelegateVote = ()=>{\n        setShowDelegateVote(true);\n    };\n    const confirmDelegateVote = (delegateData)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowDelegateVote(false);\n            showToast('投票权委托成功');\n        }, 1500);\n    };\n    const handleCommitteeDetail = (committee)=>{\n        setSelectedCommittee(committee);\n        setShowCommitteeDetail(true);\n    };\n    const formatNumber = (num)=>{\n        if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';\n        if (num >= 1000) return (num / 1000).toFixed(1) + 'k';\n        return num.toString();\n    };\n    const getSpaceTypeColor = (type)=>{\n        switch(type){\n            case 'public':\n                return 'bg-green-500';\n            case 'private':\n                return 'bg-red-500';\n            case 'premium':\n                return 'bg-yellow-500';\n            case 'event':\n                return 'bg-purple-500';\n            default:\n                return 'bg-gray-500';\n        }\n    };\n    const getSpaceTypeText = (type)=>{\n        switch(type){\n            case 'public':\n                return '公开';\n            case 'private':\n                return '私密';\n            case 'premium':\n                return '高级';\n            case 'event':\n                return '活动';\n            default:\n                return '未知';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-green-400 to-teal-500 rounded-2xl flex items-center justify-center mb-4 mx-auto animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white font-bold text-2xl\",\n                            children: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-lg mb-2\",\n                        children: \"社交元宇宙\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-sm\",\n                        children: \"连接虚拟空间中...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                lineNumber: 525,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n            lineNumber: 524,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex flex-col h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/20 backdrop-blur-lg border-b border-white/10 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-green-400 to-teal-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-sm\",\n                                                    children: \"\\uD83D\\uDE80\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-lg font-bold\",\n                                                        children: \"社交元宇宙\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-300\",\n                                                        children: \"虚拟空间 • 实时聊天 • 社交网络\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1 bg-white/20 rounded-full px-2 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs\",\n                                                    children: \"1.2k 在线\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-white/10 rounded-lg p-1 overflow-x-auto\",\n                                children: [\n                                    {\n                                        key: 'spaces',\n                                        label: '虚拟空间',\n                                        icon: '🌌'\n                                    },\n                                    {\n                                        key: 'chat',\n                                        label: '实时聊天',\n                                        icon: '💬'\n                                    },\n                                    {\n                                        key: 'community',\n                                        label: '社区群组',\n                                        icon: '👥'\n                                    },\n                                    {\n                                        key: 'friends',\n                                        label: '好友动态',\n                                        icon: '🤝'\n                                    },\n                                    {\n                                        key: 'nft',\n                                        label: 'NFT市场',\n                                        icon: '🎨'\n                                    },\n                                    {\n                                        key: 'dao',\n                                        label: 'DAO治理',\n                                        icon: '🏛️'\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.key),\n                                        className: \"flex-shrink-0 flex items-center justify-center space-x-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === tab.key ? 'bg-green-500 text-white' : 'text-white/70 hover:text-white'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.icon\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"whitespace-nowrap\",\n                                                children: tab.label\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, tab.key, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: [\n                            activeTab === 'spaces' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full overflow-y-auto p-4 space-y-4\",\n                                children: spaces.map((space, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                    children: space.host.avatar\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-bold text-white\",\n                                                                            children: space.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-green-300\",\n                                                                            children: space.host.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 607,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 text-xs text-green-400\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                space.currentUsers,\n                                                                                \"/\",\n                                                                                space.capacity\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 \".concat(getSpaceTypeColor(space.spaceType), \" rounded-full text-xs font-medium mt-1 inline-block\"),\n                                                                    children: getSpaceTypeText(space.spaceType)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 615,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-300 mb-3\",\n                                                    children: space.description\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: space.features.slice(0, 2).map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-green-500/20 rounded text-xs\",\n                                                                    children: feature\n                                                                }, idx, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleEnterSpace(space),\n                                                            className: \"px-4 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium hover:from-green-600 hover:to-teal-600 transition-colors\",\n                                                            children: \"\\uD83D\\uDE80 进入空间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, space.id, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 border-b border-white/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2 overflow-x-auto\",\n                                            children: chatRooms.map((room)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedChatRoom(room.id),\n                                                    className: \"flex-shrink-0 flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors \".concat(selectedChatRoom === room.id ? 'bg-green-500 border-green-500 text-white' : 'bg-white/10 border-white/20 text-gray-300 hover:border-white/40'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: room.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: room.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs bg-white/20 px-1 rounded\",\n                                                            children: room.members\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, room.id, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 overflow-y-auto p-4 space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: (_chatRooms_find = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find === void 0 ? void 0 : _chatRooms_find.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-white\",\n                                                        children: (_chatRooms_find1 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find1 === void 0 ? void 0 : _chatRooms_find1.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: (_chatRooms_find2 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find2 === void 0 ? void 0 : _chatRooms_find2.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-400 mt-1\",\n                                                        children: [\n                                                            (_chatRooms_find3 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find3 === void 0 ? void 0 : _chatRooms_find3.members,\n                                                            \" 成员在线\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this),\n                                            [\n                                                {\n                                                    id: 1,\n                                                    user: '🏗️ 建筑师小王',\n                                                    message: '刚完成了一个AI辅助的建筑设计，效果很棒！',\n                                                    time: '2分钟前',\n                                                    isMe: false,\n                                                    avatar: '🏗️'\n                                                },\n                                                {\n                                                    id: 2,\n                                                    user: '我',\n                                                    message: '能分享一下设计图吗？很想看看AI的效果',\n                                                    time: '1分钟前',\n                                                    isMe: true,\n                                                    avatar: '👨‍💻'\n                                                },\n                                                {\n                                                    id: 3,\n                                                    user: '🎨 AI艺术家',\n                                                    message: '我也在用AI创作NFT，最近很火呢',\n                                                    time: '30秒前',\n                                                    isMe: false,\n                                                    avatar: '🎨'\n                                                },\n                                                {\n                                                    id: 4,\n                                                    user: '🌐 Web3开发者',\n                                                    message: '有人想合作开发DApp吗？',\n                                                    time: '刚刚',\n                                                    isMe: false,\n                                                    avatar: '🌐'\n                                                }\n                                            ].map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(msg.isMe ? 'justify-end' : 'justify-start'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-w-[80%] flex \".concat(msg.isMe ? 'flex-row-reverse' : 'flex-row', \" items-start space-x-2\"),\n                                                        children: [\n                                                            !msg.isMe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-sm\",\n                                                                children: msg.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\".concat(msg.isMe ? 'bg-green-500 mr-2' : 'bg-white/10', \" rounded-lg p-3\"),\n                                                                children: [\n                                                                    !msg.isMe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-green-300 mb-1\",\n                                                                        children: msg.user\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 39\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-white\",\n                                                                        children: msg.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: msg.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 695,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, msg.id, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 pb-20 border-t border-white/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleFileUpload,\n                                                    className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-gray-400 hover:text-white transition-colors\",\n                                                    children: \"\\uD83D\\uDCCE\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyDown: (e)=>e.key === 'Enter' && handleSendMessage(),\n                                                    placeholder: \"在 \".concat((_chatRooms_find4 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find4 === void 0 ? void 0 : _chatRooms_find4.name, \" 中发消息...\"),\n                                                    className: \"flex-1 bg-white/10 text-white px-4 py-3 rounded-full border border-white/20 focus:border-green-500 focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowEmojiPicker(true),\n                                                    className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-gray-400 hover:text-white transition-colors\",\n                                                    children: \"\\uD83D\\uDE0A\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleSendMessage,\n                                                    disabled: !newMessage.trim() || sendingMessage,\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center transition-colors \".concat(!newMessage.trim() || sendingMessage ? 'bg-gray-600 cursor-not-allowed' : 'bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl\",\n                                                        children: sendingMessage ? '⏳' : '🚀'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'community' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full overflow-y-auto p-4 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"⭐\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"推荐群组\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: communityGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                                children: group.icon\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 755,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"font-bold text-white\",\n                                                                                        children: group.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                        lineNumber: 759,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-green-300\",\n                                                                                        children: [\n                                                                                            group.members.toLocaleString(),\n                                                                                            \" 成员\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                        lineNumber: 760,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 758,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 rounded-full \".concat(group.activity === 'high' ? 'bg-green-400' : group.activity === 'medium' ? 'bg-yellow-400' : 'bg-gray-400')\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 764,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: group.activity === 'high' ? '活跃' : group.activity === 'medium' ? '一般' : '较少'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 768,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 763,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-300 mb-3\",\n                                                                children: group.description\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-2 mb-3\",\n                                                                children: group.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 bg-green-500/20 rounded text-xs text-green-300\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            tag\n                                                                        ]\n                                                                    }, idx, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 777,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400 capitalize\",\n                                                                        children: group.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>group.isJoined ? handleLeaveGroup(group.id) : handleJoinGroup(group.id),\n                                                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(group.isJoined ? 'bg-gray-600 text-gray-300 hover:bg-gray-500' : 'bg-gradient-to-r from-green-500 to-teal-500 text-white hover:from-green-600 hover:to-teal-600'),\n                                                                        children: group.isJoined ? '已加入' : '加入群组'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 787,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, group.id, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"\\uD83D\\uDC65\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"我的群组\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: communityGroups.filter((g)=>g.isJoined).map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center\",\n                                                                            children: group.icon\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 814,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-medium text-white\",\n                                                                                    children: group.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                    lineNumber: 818,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: [\n                                                                                        group.members.toLocaleString(),\n                                                                                        \" 成员\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                    lineNumber: 819,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 817,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 813,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleEnterGroup(group.id),\n                                                                            className: \"px-3 py-1 bg-green-500 rounded-lg text-xs font-medium hover:bg-green-600 transition-colors\",\n                                                                            children: \"进入\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 823,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleGroupSettings(group.id),\n                                                                            className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium text-gray-400 hover:bg-white/20 hover:text-white transition-colors\",\n                                                                            children: \"设置\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 829,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 822,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 812,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, group.id, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 809,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"➕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"创建群组\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300 mb-4\",\n                                                children: \"创建专属的专业群组，聚集志同道合的伙伴，共同探讨技术和创意。\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 848,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCreateGroup,\n                                                className: \"w-full py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-white font-medium hover:from-green-600 hover:to-teal-600 transition-colors\",\n                                                children: \"\\uD83D\\uDE80 创建新群组\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'friends' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full overflow-y-auto p-4 space-y-4\",\n                                children: [\n                                    {\n                                        id: 1,\n                                        user: '🏗️ 建筑师小王',\n                                        action: '发布了新的BIM模型',\n                                        content: 'AI驱动的智慧建筑设计',\n                                        time: '5分钟前',\n                                        likes: 23\n                                    },\n                                    {\n                                        id: 2,\n                                        user: '🎨 AI艺术家',\n                                        action: '创建了NFT作品',\n                                        content: '赛博朋克风格的未来城市',\n                                        time: '15分钟前',\n                                        likes: 45\n                                    },\n                                    {\n                                        id: 3,\n                                        user: '🚀 元宇宙设计师',\n                                        action: '加入了虚拟空间',\n                                        content: '建筑师专业交流会',\n                                        time: '30分钟前',\n                                        likes: 12\n                                    }\n                                ].map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: activity.user.split(' ')[0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: activity.user.split(' ')[1]\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: [\n                                                                        \" \",\n                                                                        activity.action\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 880,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-300 mt-1\",\n                                                            children: activity.content\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 884,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mt-2 text-xs text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: activity.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"flex items-center space-x-1 text-green-400\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"❤️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 888,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: activity.likes\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 889,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 887,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"\\uD83D\\uDCAC 回复\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 891,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 885,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, activity.id, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'nft' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full overflow-y-auto p-4 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"\\uD83C\\uDFA8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 906,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"NFT市场\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xl font-bold text-purple-400\",\n                                                                children: \"12,456\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-purple-200\",\n                                                                children: \"总NFT数量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 912,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 910,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xl font-bold text-green-400\",\n                                                                children: \"2,890\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 915,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-green-200\",\n                                                                children: \"活跃创作者\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 916,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xl font-bold text-blue-400\",\n                                                                children: \"156.8 ETH\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 919,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-200\",\n                                                                children: \"24h交易量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 920,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 918,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xl font-bold text-yellow-400\",\n                                                                children: \"0.45 ETH\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 923,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-yellow-200\",\n                                                                children: \"平均价格\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 924,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 909,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"\\uD83D\\uDD25 热门NFT\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: nftMarketplace.map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 bg-gray-600 rounded-lg overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: nft.image,\n                                                                        alt: nft.title,\n                                                                        className: \"w-full h-full object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 937,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 936,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 p-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"font-medium text-white text-sm\",\n                                                                                    children: nft.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                    lineNumber: 945,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-1\",\n                                                                                    children: [\n                                                                                        nft.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-blue-400\",\n                                                                                            children: \"✓\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                            lineNumber: 947,\n                                                                                            columnNumber: 48\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"px-2 py-1 rounded-full text-xs \".concat(nft.rarity === 'legendary' ? 'bg-yellow-500' : nft.rarity === 'epic' ? 'bg-purple-500' : nft.rarity === 'rare' ? 'bg-blue-500' : 'bg-gray-500'),\n                                                                                            children: nft.rarity\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                            lineNumber: 948,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                    lineNumber: 946,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 944,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 mb-2\",\n                                                                            children: nft.creator\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 957,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-green-400 font-bold text-sm\",\n                                                                                            children: nft.price\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                            lineNumber: 960,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-400\",\n                                                                                            children: [\n                                                                                                \"≈ $\",\n                                                                                                (parseFloat(nft.price) * 2500).toFixed(0)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                            lineNumber: 961,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                    lineNumber: 959,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-1 text-xs text-gray-400\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"❤️\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                                    lineNumber: 965,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: nft.likes\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                                    lineNumber: 966,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                            lineNumber: 964,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>handleBuyNFT(nft),\n                                                                                            className: \"px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-xs font-medium hover:from-purple-600 hover:to-pink-600 transition-colors\",\n                                                                                            children: \"购买\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                            lineNumber: 968,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                    lineNumber: 963,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 958,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 943,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, nft.id, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"\\uD83D\\uDDBC️ 我的NFT\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 985,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3\",\n                                                children: [\n                                                    {\n                                                        id: 'my-1',\n                                                        title: '工程设计图',\n                                                        price: '0.2 ETH',\n                                                        status: 'owned'\n                                                    },\n                                                    {\n                                                        id: 'my-2',\n                                                        title: 'AI生成艺术',\n                                                        price: '0.15 ETH',\n                                                        status: 'selling'\n                                                    }\n                                                ].map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square bg-gray-600 rounded-lg mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 992,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"font-medium text-white text-sm mb-1\",\n                                                                children: nft.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-400 text-xs font-bold\",\n                                                                        children: nft.price\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 995,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(nft.status === 'owned' ? 'bg-blue-500' : 'bg-green-500'),\n                                                                        children: nft.status === 'owned' ? '持有' : '出售中'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 994,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, nft.id, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 991,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 986,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCreateNFT,\n                                                className: \"w-full mt-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-white font-medium hover:from-purple-600 hover:to-pink-600 transition-colors\",\n                                                children: \"\\uD83C\\uDFA8 创建NFT\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1005,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"\\uD83D\\uDCC2 NFT分类\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3\",\n                                                children: [\n                                                    {\n                                                        name: '建筑设计',\n                                                        icon: '🏗️',\n                                                        count: 1234\n                                                    },\n                                                    {\n                                                        name: 'AI艺术',\n                                                        icon: '🤖',\n                                                        count: 2890\n                                                    },\n                                                    {\n                                                        name: '概念设计',\n                                                        icon: '💡',\n                                                        count: 567\n                                                    },\n                                                    {\n                                                        name: '3D模型',\n                                                        icon: '🎯',\n                                                        count: 890\n                                                    }\n                                                ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleNFTCategory(category.name),\n                                                        className: \"bg-white/5 rounded-lg p-3 text-left hover:bg-white/10 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg\",\n                                                                        children: category.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1029,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-white text-sm\",\n                                                                        children: category.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1030,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1028,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: [\n                                                                    category.count,\n                                                                    \" 个NFT\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1032,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, category.name, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1016,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 1014,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'dao' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full overflow-y-auto p-4 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"\\uD83C\\uDFDB️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"NextGen DAO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-400\",\n                                                                children: \"25,680\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1051,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-green-200\",\n                                                                children: \"NGT持有者\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1052,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1050,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-blue-400\",\n                                                                children: \"156\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1055,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-200\",\n                                                                children: \"活跃提案\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1054,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-400\",\n                                                                children: \"89%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1059,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-purple-200\",\n                                                                children: \"参与率\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1060,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-yellow-400\",\n                                                                children: \"4.8M\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1063,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-yellow-200\",\n                                                                children: \"总投票权\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1064,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1062,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1049,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/5 rounded-lg p-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-300\",\n                                                    children: \"NextGen DAO是一个去中心化自治组织，由社区成员共同治理平台的发展方向。 持有NGT代币即可参与提案投票，共同决定平台的未来。\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1068,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1067,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"活跃提案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1077,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    {\n                                                        id: 'prop-042',\n                                                        title: '降低平台交易手续费至2%',\n                                                        description: '建议将NFT交易手续费从3%降低至2%，以提高平台竞争力',\n                                                        status: 'voting',\n                                                        votes: {\n                                                            for: 15420,\n                                                            against: 3280\n                                                        },\n                                                        endTime: '2025-01-20',\n                                                        category: 'economic'\n                                                    },\n                                                    {\n                                                        id: 'prop-043',\n                                                        title: '新增VR虚拟展厅功能',\n                                                        description: '为创作者提供VR虚拟展厅，展示NFT作品集',\n                                                        status: 'voting',\n                                                        votes: {\n                                                            for: 12890,\n                                                            against: 1560\n                                                        },\n                                                        endTime: '2025-01-22',\n                                                        category: 'feature'\n                                                    },\n                                                    {\n                                                        id: 'prop-044',\n                                                        title: '建立创作者扶持基金',\n                                                        description: '从平台收益中拨出10%建立创作者扶持基金',\n                                                        status: 'passed',\n                                                        votes: {\n                                                            for: 18920,\n                                                            against: 2340\n                                                        },\n                                                        endTime: '2025-01-15',\n                                                        category: 'community'\n                                                    }\n                                                ].map((proposal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: proposal.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1110,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(proposal.status === 'voting' ? 'bg-yellow-500' : proposal.status === 'passed' ? 'bg-green-500' : 'bg-red-500'),\n                                                                        children: proposal.status === 'voting' ? '投票中' : proposal.status === 'passed' ? '已通过' : '未通过'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1111,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1109,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-300 mb-3\",\n                                                                children: proposal.description\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1120,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between text-xs mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-green-400\",\n                                                                                children: [\n                                                                                    \"赞成: \",\n                                                                                    proposal.votes.for.toLocaleString()\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 1125,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-400\",\n                                                                                children: [\n                                                                                    \"反对: \",\n                                                                                    proposal.votes.against.toLocaleString()\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 1126,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1124,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-white/20 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full\",\n                                                                            style: {\n                                                                                width: \"\".concat(proposal.votes.for / (proposal.votes.for + proposal.votes.against) * 100, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 1129,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1128,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1123,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: proposal.status === 'voting' ? \"截止: \".concat(proposal.endTime) : \"结束: \".concat(proposal.endTime)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1139,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    proposal.status === 'voting' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleVote(proposal.id, 'for'),\n                                                                                className: \"px-3 py-1 bg-green-500 rounded-lg text-xs font-medium hover:bg-green-600 transition-colors\",\n                                                                                children: \"赞成\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 1144,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleVote(proposal.id, 'against'),\n                                                                                className: \"px-3 py-1 bg-red-500 rounded-lg text-xs font-medium hover:bg-red-600 transition-colors\",\n                                                                                children: \"反对\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 1150,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1143,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1138,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, proposal.id, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1108,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1078,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 1076,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"专业委员会\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3\",\n                                                children: [\n                                                    {\n                                                        name: '技术委员会',\n                                                        icon: '⚙️',\n                                                        members: 12,\n                                                        description: '技术路线制定'\n                                                    },\n                                                    {\n                                                        name: '内容委员会',\n                                                        icon: '📝',\n                                                        members: 8,\n                                                        description: '内容质量监管'\n                                                    },\n                                                    {\n                                                        name: '经济委员会',\n                                                        icon: '💰',\n                                                        members: 10,\n                                                        description: '代币经济设计'\n                                                    },\n                                                    {\n                                                        name: '仲裁委员会',\n                                                        icon: '⚖️',\n                                                        members: 6,\n                                                        description: '争议处理'\n                                                    }\n                                                ].map((committee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg\",\n                                                                        children: committee.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1176,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-white text-sm\",\n                                                                        children: committee.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1177,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mb-2\",\n                                                                children: committee.description\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1179,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-green-400\",\n                                                                        children: [\n                                                                            committee.members,\n                                                                            \" 成员\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1181,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleCommitteeDetail(committee),\n                                                                        className: \"text-xs text-blue-400 hover:text-blue-300 transition-colors\",\n                                                                        children: \"查看详情\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1182,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1180,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, committee.name, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1174,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 1165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"我的治理参与\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-3 gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xl font-bold text-purple-400\",\n                                                                children: \"1,250\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1199,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-purple-200\",\n                                                                children: \"投票权重\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1200,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xl font-bold text-green-400\",\n                                                                children: \"15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1203,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-green-200\",\n                                                                children: \"参与提案\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1204,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1202,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xl font-bold text-yellow-400\",\n                                                                children: \"89%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1207,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-yellow-200\",\n                                                                children: \"参与率\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 1208,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1206,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCreateProposal,\n                                                        className: \"flex-1 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium hover:from-green-600 hover:to-teal-600 transition-colors\",\n                                                        children: \"创建提案\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleDelegateVote,\n                                                        className: \"flex-1 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-purple-600 transition-colors\",\n                                                        children: \"委托投票\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 1218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1211,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 1195,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                lineNumber: 539,\n                columnNumber: 7\n            }, this),\n            showSuccessToast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1235,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: toastMessage\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1236,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 1234,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                lineNumber: 1233,\n                columnNumber: 9\n            }, this),\n            showEnterSpace && selectedSpace && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-3\",\n                                    children: selectedSpace.host.avatar\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1246,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white mb-2\",\n                                    children: selectedSpace.title\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1247,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: selectedSpace.description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1248,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1245,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400\",\n                                            children: \"当前人数\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400\",\n                                            children: [\n                                                selectedSpace.currentUsers,\n                                                \"/\",\n                                                selectedSpace.capacity\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1252,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400\",\n                                            children: \"空间类型\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1257,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-400\",\n                                            children: getSpaceTypeText(selectedSpace.spaceType)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1258,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1256,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400\",\n                                            children: \"主要功能\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1261,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-400\",\n                                            children: selectedSpace.features.slice(0, 2).join(', ')\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1262,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1260,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1251,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowEnterSpace(false),\n                                    className: \"flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmEnterSpace,\n                                    disabled: isLoading,\n                                    className: \"flex-1 py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl text-white font-medium hover:from-green-600 hover:to-teal-600 transition-colors disabled:opacity-50\",\n                                    children: isLoading ? '进入中...' : '确认进入'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1273,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1266,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 1244,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                lineNumber: 1243,\n                columnNumber: 9\n            }, this),\n            showEmojiPicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"选择表情\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1290,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowEmojiPicker(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1291,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1289,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-6 gap-3 mb-4\",\n                            children: [\n                                '😀',\n                                '😃',\n                                '😄',\n                                '😁',\n                                '😆',\n                                '😅',\n                                '😂',\n                                '🤣',\n                                '😊',\n                                '😇',\n                                '🙂',\n                                '🙃',\n                                '😉',\n                                '😌',\n                                '😍',\n                                '🥰',\n                                '😘',\n                                '😗',\n                                '😙',\n                                '😚',\n                                '😋',\n                                '😛',\n                                '😝',\n                                '😜',\n                                '🤪',\n                                '🤨',\n                                '🧐',\n                                '🤓',\n                                '😎',\n                                '🤩'\n                            ].map((emoji)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleEmojiSelect(emoji),\n                                    className: \"text-2xl p-2 hover:bg-white/10 rounded-lg transition-colors\",\n                                    children: emoji\n                                }, emoji, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1301,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1299,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 1288,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                lineNumber: 1287,\n                columnNumber: 9\n            }, this),\n            showFileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"上传文件\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowFileUpload(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1320,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1318,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-2 border-dashed border-white/20 rounded-lg p-8 text-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDCC1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1329,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-2\",\n                                    children: \"点击或拖拽上传文件\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1330,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"支持图片、视频、文档等格式\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1331,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    multiple: true,\n                                    className: \"hidden\",\n                                    onChange: (e)=>e.target.files && confirmFileUpload(e.target.files)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1332,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1328,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                var _document_querySelector;\n                                return (_document_querySelector = document.querySelector('input[type=\"file\"]')) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.click();\n                            },\n                            className: \"w-full py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl text-white font-medium hover:from-green-600 hover:to-teal-600 transition-colors\",\n                            children: \"选择文件\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1340,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 1317,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                lineNumber: 1316,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n        lineNumber: 537,\n        columnNumber: 5\n    }, this);\n}\n_s(SocialPage, \"kRtG0ufnbvKbSTX1o4r7uCim4y8=\");\n_c = SocialPage;\nvar _c;\n$RefreshReg$(_c, \"SocialPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/social/page.tsx\n"));

/***/ })

});