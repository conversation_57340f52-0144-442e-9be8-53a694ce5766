"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _wechat_video_homepage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wechat-video-homepage */ \"(app-pages-browser)/./src/app/wechat-video-homepage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_wechat_video_homepage__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUV5RDtBQUUxQyxTQUFTQztJQUN0QixxQkFBTyw4REFBQ0QsOERBQW1CQTs7Ozs7QUFDN0I7S0FGd0JDIiwic291cmNlcyI6WyJFOlxcMjAyNVxcMjAyNTA3MTBcXHpodW1lbmd5dW4tZnJvbnRlbmRcXG1vYmlsZVxcc3JjXFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgV2VDaGF0VmlkZW9Ib21lcGFnZSBmcm9tICcuL3dlY2hhdC12aWRlby1ob21lcGFnZSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZVBhZ2UoKSB7XG4gIHJldHVybiA8V2VDaGF0VmlkZW9Ib21lcGFnZSAvPlxufVxuIl0sIm5hbWVzIjpbIldlQ2hhdFZpZGVvSG9tZXBhZ2UiLCJIb21lUGFnZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/wechat-video-homepage.tsx":
/*!*******************************************!*\
  !*** ./src/app/wechat-video-homepage.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeChatVideoHomepage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WeChatVideoHomepage() {\n    _s();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFollowing, setIsFollowing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShare, setShowShare] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60);\n    // 视频内容数据\n    const videoContent = [\n        {\n            id: 1,\n            title: \"NextGen 2025智慧城市建设项目展示\",\n            author: \"AI建筑大师\",\n            authorAvatar: \"/api/placeholder/50/50\",\n            verified: true,\n            description: \"🏗️ 使用AI驱动的建筑设计，创造未来智慧城市综合体。集成VR漫游、智能管理和IoT控制系统。\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 18900,\n            comments: 2480,\n            shares: 1890,\n            tags: [\n                \"#AI建筑\",\n                \"#智慧城市\",\n                \"#未来设计\"\n            ],\n            location: \"深圳市南山区\",\n            music: \"原创音乐 - 未来之城\"\n        },\n        {\n            id: 2,\n            title: \"元宇宙虚拟展厅设计案例\",\n            author: \"VR设计师小李\",\n            authorAvatar: \"/api/placeholder/50/50\",\n            verified: true,\n            description: \"🌌 创新的元宇宙虚拟展厅设计，支持多人实时交互和3D展示，为企业提供沉浸式展示体验。\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 12340,\n            comments: 1567,\n            shares: 890,\n            tags: [\n                \"#元宇宙\",\n                \"#VR设计\",\n                \"#虚拟展厅\"\n            ],\n            location: \"北京市朝阳区\",\n            music: \"电子音乐 - 数字空间\"\n        },\n        {\n            id: 3,\n            title: \"AI驱动VR学习实验室\",\n            author: \"教育科技专家\",\n            authorAvatar: \"/api/placeholder/50/50\",\n            verified: true,\n            description: \"🎓 GPT-5个性化VR学习空间，支持物理化学生物实验和区块链认证，革新教育体验。\",\n            videoUrl: \"/api/placeholder/400/700\",\n            likes: 23450,\n            comments: 3210,\n            shares: 1456,\n            tags: [\n                \"#AI教育\",\n                \"#VR学习\",\n                \"#未来教育\"\n            ],\n            location: \"上海市浦东新区\",\n            music: \"轻音乐 - 知识之光\"\n        }\n    ];\n    const currentContent = videoContent[currentIndex];\n    // 格式化数字\n    const formatNumber = (num)=>{\n        if (num >= 10000) {\n            return (num / 10000).toFixed(1) + 'w';\n        } else if (num >= 1000) {\n            return (num / 1000).toFixed(1) + 'k';\n        }\n        return num.toString();\n    };\n    // 格式化时间\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // 交互处理函数\n    const handleLike = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleLike]\": ()=>setIsLiked(!isLiked)\n    }[\"WeChatVideoHomepage.useCallback[handleLike]\"], [\n        isLiked\n    ]);\n    const handleFollow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleFollow]\": ()=>setIsFollowing(!isFollowing)\n    }[\"WeChatVideoHomepage.useCallback[handleFollow]\"], [\n        isFollowing\n    ]);\n    const handleComment = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleComment]\": ()=>setShowComments(true)\n    }[\"WeChatVideoHomepage.useCallback[handleComment]\"], []);\n    const handleShare = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[handleShare]\": ()=>setShowShare(true)\n    }[\"WeChatVideoHomepage.useCallback[handleShare]\"], []);\n    const togglePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[togglePlay]\": ()=>setIsPlaying(!isPlaying)\n    }[\"WeChatVideoHomepage.useCallback[togglePlay]\"], [\n        isPlaying\n    ]);\n    // 视频切换函数\n    const nextVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[nextVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[nextVideo]\": (prev)=>(prev + 1) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[nextVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n            setCurrentTime(0);\n        }\n    }[\"WeChatVideoHomepage.useCallback[nextVideo]\"], [\n        videoContent.length\n    ]);\n    const prevVideo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeChatVideoHomepage.useCallback[prevVideo]\": ()=>{\n            setCurrentIndex({\n                \"WeChatVideoHomepage.useCallback[prevVideo]\": (prev)=>(prev - 1 + videoContent.length) % videoContent.length\n            }[\"WeChatVideoHomepage.useCallback[prevVideo]\"]);\n            setIsLiked(false);\n            setIsFollowing(false);\n            setIsPlaying(true);\n            setCurrentTime(0);\n        }\n    }[\"WeChatVideoHomepage.useCallback[prevVideo]\"], [\n        videoContent.length\n    ]);\n    // 触摸手势处理\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientY);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientY);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isUpSwipe = distance > 50;\n        const isDownSwipe = distance < -50;\n        if (isUpSwipe) {\n            nextVideo();\n        }\n        if (isDownSwipe) {\n            prevVideo();\n        }\n    };\n    // 自动播放进度\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeChatVideoHomepage.useEffect\": ()=>{\n            if (isPlaying) {\n                const timer = setInterval({\n                    \"WeChatVideoHomepage.useEffect.timer\": ()=>{\n                        setCurrentTime({\n                            \"WeChatVideoHomepage.useEffect.timer\": (prev)=>{\n                                if (prev >= duration) {\n                                    nextVideo();\n                                    return 0;\n                                }\n                                return prev + 1;\n                            }\n                        }[\"WeChatVideoHomepage.useEffect.timer\"]);\n                    }\n                }[\"WeChatVideoHomepage.useEffect.timer\"], 1000);\n                return ({\n                    \"WeChatVideoHomepage.useEffect\": ()=>clearInterval(timer)\n                })[\"WeChatVideoHomepage.useEffect\"];\n            }\n        }\n    }[\"WeChatVideoHomepage.useEffect\"], [\n        isPlaying,\n        duration,\n        nextVideo\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-black relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                onClick: togglePlay,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full bg-gradient-to-br from-purple-900 via-blue-800 to-indigo-900 relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-40 h-40 bg-white/20 rounded-full flex items-center justify-center mb-6 mx-auto backdrop-blur-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-6xl\",\n                                            children: currentIndex === 0 ? '🏗️' : currentIndex === 1 ? '🌌' : '🎓'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold mb-2 px-4\",\n                                        children: currentContent.title\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm opacity-80 px-6\",\n                                        children: currentContent.description\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        !isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-0 h-0 border-l-[20px] border-l-white border-y-[12px] border-y-transparent ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 right-0 h-12 bg-gradient-to-b from-black/50 to-transparent z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-4 pt-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white text-sm\",\n                            children: \"视频号\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-white rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-white rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-white rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-4 bottom-32 flex flex-col items-center space-y-6 z-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-full overflow-hidden border-2 border-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: currentContent.authorAvatar,\n                                    alt: currentContent.author,\n                                    width: 48,\n                                    height: 48,\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            !isFollowing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                onClick: handleFollow,\n                                whileTap: {\n                                    scale: 0.8\n                                },\n                                className: \"absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-xs font-bold\",\n                                    children: \"+\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                        onClick: handleLike,\n                        whileTap: {\n                            scale: 0.8\n                        },\n                        className: \"flex flex-col items-center space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-full flex items-center justify-center transition-all \".concat(isLiked ? 'bg-red-500' : 'bg-black/20 backdrop-blur-sm'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    animate: isLiked ? {\n                                        scale: [\n                                            1,\n                                            1.2,\n                                            1\n                                        ]\n                                    } : {},\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: \"❤️\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white text-xs\",\n                                children: formatNumber(currentContent.likes + (isLiked ? 1 : 0))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleComment,\n                        className: \"flex flex-col items-center space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-full bg-black/20 backdrop-blur-sm flex items-center justify-center\",\n                                children: \"\\uD83D\\uDCAC\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white text-xs\",\n                                children: formatNumber(currentContent.comments)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleShare,\n                        className: \"flex flex-col items-center space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-full bg-black/20 backdrop-blur-sm flex items-center justify-center\",\n                                children: \"↗️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white text-xs\",\n                                children: formatNumber(currentContent.shares)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center space-y-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            animate: {\n                                rotate: isPlaying ? 360 : 0\n                            },\n                            transition: {\n                                duration: 3,\n                                repeat: isPlaying ? Infinity : 0,\n                                ease: \"linear\"\n                            },\n                            className: \"w-12 h-12 rounded-full bg-black/20 backdrop-blur-sm flex items-center justify-center\",\n                            children: \"\\uD83C\\uDFB5\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-medium\",\n                                children: [\n                                    \"@\",\n                                    currentContent.author\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            currentContent.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-xs\",\n                                    children: \"✓\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-sm mb-3 leading-relaxed\",\n                        children: currentContent.description\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 mb-3\",\n                        children: currentContent.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-blue-300 text-sm\",\n                                children: tag\n                            }, index, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white text-xs\",\n                                children: \"\\uD83C\\uDFB5\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white text-xs opacity-80\",\n                                children: currentContent.music\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-1 bg-white/20 rounded-full overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full bg-white rounded-full transition-all duration-1000\",\n                            style: {\n                                width: \"\".concat(currentTime / duration * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 h-20 bg-black/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-around h-full px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 text-white\",\n                                    children: \"\\uD83C\\uDFE0\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-xs\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 text-gray-400\",\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"发现\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 text-gray-400\",\n                                    children: \"➕\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"发布\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 text-gray-400\",\n                                    children: \"\\uD83D\\uDCAC\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"消息\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 text-gray-400\",\n                                    children: \"\\uD83D\\uDC64\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs\",\n                                    children: \"我的\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: showComments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 z-50 flex items-end\",\n                    onClick: ()=>setShowComments(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            y: '100%'\n                        },\n                        animate: {\n                            y: 0\n                        },\n                        exit: {\n                            y: '100%'\n                        },\n                        className: \"w-full bg-white rounded-t-3xl max-h-[70vh] overflow-hidden\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border-b border-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: [\n                                            formatNumber(currentContent.comments),\n                                            \"条评论\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowComments(false),\n                                        className: \"w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                                children: [\n                                    {\n                                        user: 'AI建筑师',\n                                        avatar: '🏗️',\n                                        content: '这个智慧城市设计太震撼了！',\n                                        time: '2分钟前',\n                                        likes: 128\n                                    },\n                                    {\n                                        user: '科技爱好者',\n                                        avatar: '🤖',\n                                        content: 'NextGen 2025真的是未来趋势',\n                                        time: '5分钟前',\n                                        likes: 89\n                                    },\n                                    {\n                                        user: '城市规划师',\n                                        avatar: '🏙️',\n                                        content: '想了解更多技术细节',\n                                        time: '8分钟前',\n                                        likes: 56\n                                    },\n                                    {\n                                        user: '学生小王',\n                                        avatar: '🎓',\n                                        content: '学到了很多，感谢分享！',\n                                        time: '10分钟前',\n                                        likes: 34\n                                    }\n                                ].map((comment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: comment.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: comment.user\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: comment.time\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 mb-2\",\n                                                        children: comment.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"flex items-center space-x-1 text-gray-500 hover:text-red-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"❤️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: comment.likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-gray-500 hover:text-blue-500 text-sm\",\n                                                                children: \"回复\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-gray-100 bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"我\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"说点什么...\",\n                                                    className: \"flex-1 bg-white border border-gray-200 rounded-full px-4 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-500 hover:bg-blue-600 rounded-full px-6 py-2 text-white font-medium\",\n                                                    children: \"发送\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: showShare && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 z-50 flex items-end\",\n                    onClick: ()=>setShowShare(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            y: '100%'\n                        },\n                        animate: {\n                            y: 0\n                        },\n                        exit: {\n                            y: '100%'\n                        },\n                        className: \"w-full bg-white rounded-t-3xl p-6\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"分享到\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowShare(false),\n                                        className: \"w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-4 gap-6 mb-8\",\n                                children: [\n                                    {\n                                        name: '微信好友',\n                                        icon: '💬',\n                                        color: 'bg-green-500'\n                                    },\n                                    {\n                                        name: '朋友圈',\n                                        icon: '🌟',\n                                        color: 'bg-green-600'\n                                    },\n                                    {\n                                        name: 'QQ好友',\n                                        icon: '🐧',\n                                        color: 'bg-blue-500'\n                                    },\n                                    {\n                                        name: 'QQ空间',\n                                        icon: '⭐',\n                                        color: 'bg-yellow-500'\n                                    },\n                                    {\n                                        name: '微博',\n                                        icon: '📱',\n                                        color: 'bg-red-500'\n                                    },\n                                    {\n                                        name: '抖音',\n                                        icon: '🎵',\n                                        color: 'bg-black'\n                                    },\n                                    {\n                                        name: '复制链接',\n                                        icon: '🔗',\n                                        color: 'bg-gray-600'\n                                    },\n                                    {\n                                        name: '保存视频',\n                                        icon: '📥',\n                                        color: 'bg-purple-500'\n                                    }\n                                ].map((platform, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-14 h-14 \".concat(platform.color, \" rounded-2xl flex items-center justify-center\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: platform.icon\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700 text-sm text-center\",\n                                                children: platform.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowShare(false),\n                                className: \"w-full py-4 bg-gray-100 hover:bg-gray-200 rounded-2xl text-gray-700 font-medium\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\wechat-video-homepage.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(WeChatVideoHomepage, \"49Cd05SSbssn++2+cXRVLNyYi7w=\");\n_c = WeChatVideoHomepage;\nvar _c;\n$RefreshReg$(_c, \"WeChatVideoHomepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/wechat-video-homepage.tsx\n"));

/***/ })

});