'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import CreatorDashboard from '@/components/CreatorDashboard'
import PointsManager from '@/components/PointsManager'
import { UserPoints } from '@/services/creatorEconomyService'


interface UserProfile {
  id: string
  name: string
  avatar: string
  title: string
  bio: string
  location: string
  verified: boolean
  stats: {
    projects: number
    followers: number
    following: number
    likes: number
  }
  skills: string[]
  achievements: {
    id: string
    name: string
    icon: string
    description: string
    unlockedAt: string
  }[]
  // Web3 DID 身份信息
  did: {
    address: string
    reputation: number
    nftCount: number
    tokenBalance: number
    verifications: {
      biometric: boolean
      social: boolean
      professional: boolean
      multiSig: boolean
    }
    credentials: {
      id: string
      type: 'education' | 'skill' | 'work' | 'certification'
      title: string
      issuer: string
      verifiedAt: string
      onChain: boolean
    }[]
  }
}

interface Project {
  id: string
  title: string
  description: string
  thumbnail: string
  likes: number
  views: number
  createdAt: string
}

export default function OptimizedProfilePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<'projects' | 'achievements' | 'stats' | 'did' | 'web3' | 'creator' | 'points'>('projects')
  const [isClient, setIsClient] = useState(false)


  // 客户端状态设置
  useEffect(() => {
    setIsClient(true)
  }, [])

  // NextGen 2025 用户资料数据
  const userProfile: UserProfile = {
    id: 'user-1',
    name: 'NextGen创作者',
    avatar: '🚀',
    title: 'AI原生数字生活先锋',
    bio: '探索NextGen 2025数字生活的先锋用户，专注于AI辅助创作、Web3创新和元宇宙体验。跨越七大生态系统的全能创作者。',
    location: '数字元宇宙',
    verified: true,
    stats: {
      projects: 156,
      followers: 15600,
      following: 890,
      likes: 234000
    },
    skills: ['AI创作', 'Web3开发', 'NFT设计', 'VR/AR', '元宇宙建设', '智能合约', 'BIM+AI', '区块链'],
    achievements: [
      {
        id: 'ach-1',
        name: 'AI创作先锋',
        icon: '🧠',
        description: '使用AI工具创作100个作品',
        unlockedAt: '2025-01-15'
      },
      {
        id: 'ach-2',
        name: 'Web3创新者',
        icon: '💎',
        description: '发布50个NFT作品',
        unlockedAt: '2025-01-10'
      },
      {
        id: 'ach-3',
        name: '元宇宙建设者',
        icon: '🌌',
        description: '创建10个虚拟空间',
        unlockedAt: '2025-01-05'
      },
      {
        id: 'ach-4',
        name: '生态系统大师',
        icon: '🚀',
        description: '在所有7个生态系统中达到专家级',
        unlockedAt: '2025-01-01'
      }
    ],
    // Web3 DID 身份信息
    did: {
      address: '0x742d35Cc6634C0532925a3b8D4C0532925a3b8D4',
      reputation: 950, // 信誉评分 (0-1000)
      nftCount: 156,
      tokenBalance: 25680, // NGT代币余额
      verifications: {
        biometric: true,    // 生物识别验证
        social: true,       // 社交验证
        professional: true, // 专业认证
        multiSig: true      // 多重签名
      },
      credentials: [
        {
          id: 'cred-1',
          type: 'education',
          title: '计算机科学硕士',
          issuer: '清华大学',
          verifiedAt: '2024-06-15',
          onChain: true
        },
        {
          id: 'cred-2',
          type: 'skill',
          title: 'AI专家认证',
          issuer: 'NextGen AI学院',
          verifiedAt: '2024-12-01',
          onChain: true
        },
        {
          id: 'cred-3',
          type: 'work',
          title: '高级区块链开发工程师',
          issuer: 'Web3创新实验室',
          verifiedAt: '2024-08-20',
          onChain: true
        },
        {
          id: 'cred-4',
          type: 'certification',
          title: 'NFT创作者认证',
          issuer: 'OpenSea认证中心',
          verifiedAt: '2024-11-10',
          onChain: true
        }
      ]
    }
  }

  // NextGen 2025 用户项目数据
  const userProjects: Project[] = [
    {
      id: 'proj-1',
      title: 'AI原生智慧城市综合体',
      description: 'GPT-5驱动的智慧城市设计，集成VR漫游、区块链管理和IoT控制',
      thumbnail: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=300&h=200&fit=crop',
      likes: 8250,
      views: 45420,
      createdAt: '2025-01-15'
    },
    {
      id: 'proj-2',
      title: '元宇宙社交空间NFT',
      description: 'WebXR驱动的沉浸式社交元宇宙，支持虚拟身份和NFT商品交易',
      thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop',
      likes: 12890,
      views: 78210,
      createdAt: '2025-01-10'
    },
    {
      id: 'proj-3',
      title: 'AI驱动VR学习实验室',
      description: 'GPT-5个性化VR学习空间，支持物理化学生物实验和区块链认证',
      thumbnail: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop',
      likes: 18100,
      views: 125750,
      createdAt: '2025-01-05'
    },
    {
      id: 'proj-4',
      title: 'IoT智能家居生态系统',
      description: 'AI原生智能家居管理，支持语音手势控制、设备互联和健康监测',
      thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop',
      likes: 9500,
      views: 56400,
      createdAt: '2025-01-01'
    }
  ]

  // 格式化数字
  const formatNumber = useCallback((num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  }, [])

  // 格式化日期
  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }, [])

  if (!isClient) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-2 border-purple-400 border-t-transparent rounded-full"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 text-white">
      {/* 星空背景 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {Array.from({ length: 30 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.2, 1, 0.2],
              scale: [0.5, 1.5, 0.5]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2
            }}
          />
        ))}
      </div>

      {/* 顶部导航 */}
      <div className="sticky top-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            
            <h1 className="text-lg font-bold">个人资料</h1>

            <div className="w-10 h-10"></div>
          </div>
        </div>
      </div>

      {/* 用户信息卡片 */}
      <div className="px-4 py-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/10 backdrop-blur-sm rounded-3xl p-6 border border-white/20"
        >
          {/* 头像和基本信息 */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative">
              <div className="w-20 h-20 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-3xl">
                {userProfile.avatar}
              </div>
              {userProfile.verified && (
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
              )}
            </div>
            
            <div className="flex-1">
              <h2 className="text-xl font-bold mb-1">{userProfile.name}</h2>
              <p className="text-purple-300 text-sm mb-1">{userProfile.title}</p>
              <p className="text-purple-200 text-xs flex items-center">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {userProfile.location}
              </p>
            </div>
          </div>

          {/* 个人简介 */}
          <p className="text-purple-100 text-sm mb-6 leading-relaxed">
            {userProfile.bio}
          </p>

          {/* 统计数据 */}
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-xl font-bold text-white">{userProfile.stats.projects}</div>
              <div className="text-xs text-purple-300">项目</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-white">{formatNumber(userProfile.stats.followers)}</div>
              <div className="text-xs text-purple-300">关注者</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-white">{userProfile.stats.following}</div>
              <div className="text-xs text-purple-300">关注中</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-white">{formatNumber(userProfile.stats.likes)}</div>
              <div className="text-xs text-purple-300">获赞</div>
            </div>
          </div>

          {/* 技能标签 */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-purple-300 mb-3">专业技能</h3>
            <div className="flex flex-wrap gap-2">
              {userProfile.skills.map((skill, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-purple-500/30 text-purple-200 rounded-full text-xs border border-purple-400/30"
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-3 mb-4">
            <button className="flex-1 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-medium hover:shadow-lg transition-all">
              编辑资料
            </button>
            <button className="flex-1 py-3 bg-white/10 text-white rounded-xl font-medium hover:bg-white/20 transition-colors border border-white/20">
              分享资料
            </button>
          </div>

          {/* 合规功能入口 */}
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={() => router.push('/verification')}
              className="flex items-center space-x-3 p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-500/30 hover:bg-green-500/30 transition-all"
            >
              <div className="text-xl">✓</div>
              <div className="text-left">
                <div className="text-sm font-medium text-white">实名认证</div>
                <div className="text-xs text-green-200">身份验证</div>
              </div>
            </button>

            <button
              onClick={() => router.push('/withdraw')}
              className="flex items-center space-x-3 p-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl border border-yellow-500/30 hover:bg-yellow-500/30 transition-all"
            >
              <div className="text-xl">💰</div>
              <div className="text-left">
                <div className="text-sm font-medium text-white">收益提现</div>
                <div className="text-xs text-yellow-200">合规提现</div>
              </div>
            </button>
          </div>
        </motion.div>

        {/* 标签切换 */}
        <div className="flex space-x-4 mt-8 mb-6 overflow-x-auto">
          <button
            onClick={() => setActiveTab('projects')}
            className={`text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap ${
              activeTab === 'projects'
                ? 'text-purple-400 border-purple-400'
                : 'text-purple-200 border-transparent hover:text-purple-300'
            }`}
          >
            我的项目
          </button>
          <button
            onClick={() => setActiveTab('achievements')}
            className={`text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap ${
              activeTab === 'achievements'
                ? 'text-purple-400 border-purple-400'
                : 'text-purple-200 border-transparent hover:text-purple-300'
            }`}
          >
            成就徽章
          </button>
          <button
            onClick={() => setActiveTab('stats')}
            className={`text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap ${
              activeTab === 'stats'
                ? 'text-purple-400 border-purple-400'
                : 'text-purple-200 border-transparent hover:text-purple-300'
            }`}
          >
            数据统计
          </button>
          <button
            onClick={() => setActiveTab('did')}
            className={`text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap ${
              activeTab === 'did'
                ? 'text-purple-400 border-purple-400'
                : 'text-purple-200 border-transparent hover:text-purple-300'
            }`}
          >
            🆔 DID身份
          </button>
          <button
            onClick={() => setActiveTab('web3')}
            className={`text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap ${
              activeTab === 'web3'
                ? 'text-purple-400 border-purple-400'
                : 'text-purple-200 border-transparent hover:text-purple-300'
            }`}
          >
            💎 数字资产
          </button>
          <button
            onClick={() => setActiveTab('creator')}
            className={`text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap ${
              activeTab === 'creator'
                ? 'text-purple-400 border-purple-400'
                : 'text-purple-200 border-transparent hover:text-purple-300'
            }`}
          >
            🎨 创作者
          </button>
          <button
            onClick={() => setActiveTab('points')}
            className={`text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap ${
              activeTab === 'points'
                ? 'text-purple-400 border-purple-400'
                : 'text-purple-200 border-transparent hover:text-purple-300'
            }`}
          >
            🌟 积分
          </button>
        </div>

        {/* 内容区域 */}
        <AnimatePresence mode="wait">
          {activeTab === 'projects' && (
            <motion.div
              key="projects"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              {userProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/20 hover:border-white/30 transition-all"
                >
                  <div className="flex">
                    <div className="w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-500 flex-shrink-0">
                      <Image
                        src={project.thumbnail}
                        alt={project.title}
                        width={96}
                        height={96}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 p-4">
                      <h3 className="font-medium text-white mb-1">{project.title}</h3>
                      <p className="text-purple-200 text-sm mb-2 line-clamp-2">{project.description}</p>
                      {/* 统一交互按钮设计 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <button className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors">
                            <div className="w-6 h-6 bg-white/10 rounded-full flex items-center justify-center text-xs">
                              👍
                            </div>
                            <span className="text-xs">{formatNumber(project.likes)}</span>
                          </button>

                          <button className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors">
                            <div className="w-6 h-6 bg-white/10 rounded-full flex items-center justify-center text-xs">
                              🔄
                            </div>
                            <span className="text-xs">33</span>
                          </button>

                          <button className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors">
                            <div className="w-6 h-6 bg-white/10 rounded-full flex items-center justify-center text-xs">
                              ❤️
                            </div>
                            <span className="text-xs">45</span>
                          </button>

                          <button className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors">
                            <div className="w-6 h-6 bg-white/10 rounded-full flex items-center justify-center text-xs">
                              💬
                            </div>
                            <span className="text-xs">67</span>
                          </button>
                        </div>

                        <span className="text-xs text-purple-300">{formatDate(project.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}

          {activeTab === 'achievements' && (
            <motion.div
              key="achievements"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="grid grid-cols-2 gap-4"
            >
              {userProfile.achievements.map((achievement, index) => (
                <motion.div
                  key={achievement.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 text-center"
                >
                  <div className="text-3xl mb-2">{achievement.icon}</div>
                  <h3 className="font-medium text-white mb-1">{achievement.name}</h3>
                  <p className="text-purple-200 text-xs mb-2">{achievement.description}</p>
                  <p className="text-purple-300 text-xs">{formatDate(achievement.unlockedAt)}</p>
                </motion.div>
              ))}
            </motion.div>
          )}

          {activeTab === 'stats' && (
            <motion.div
              key="stats"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <h3 className="font-medium text-white mb-4">活跃度统计</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-purple-200">本月发布项目</span>
                    <span className="text-white font-medium">8 个</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-purple-200">本月获得点赞</span>
                    <span className="text-white font-medium">342 个</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-purple-200">本月新增关注者</span>
                    <span className="text-white font-medium">89 人</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-purple-200">平均项目评分</span>
                    <span className="text-white font-medium">4.8 ⭐</span>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* DID身份管理 */}
          {activeTab === 'did' && (
            <motion.div
              key="did"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* DID身份概览 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-white flex items-center space-x-2">
                    <span>🆔</span>
                    <span>去中心化身份 (DID)</span>
                  </h3>
                  <div className="flex items-center space-x-2">
                    <span className="px-3 py-1 bg-green-500 rounded-full text-xs font-medium">已验证</span>
                    <span className="text-yellow-400 text-sm">信誉: {userProfile.did.reputation}/1000</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400">{userProfile.did.nftCount}</div>
                    <div className="text-xs text-purple-200">NFT资产</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">{userProfile.did.tokenBalance.toLocaleString()}</div>
                    <div className="text-xs text-green-200">NGT代币</div>
                  </div>
                </div>

                <div className="bg-white/5 rounded-lg p-4">
                  <p className="text-xs text-purple-200 mb-2">DID地址:</p>
                  <div className="flex items-center justify-between">
                    <code className="text-sm text-white font-mono">{userProfile.did.address}</code>
                    <button className="text-purple-400 hover:text-purple-300">
                      <span className="text-sm">📋</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* 身份验证状态 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h4 className="text-lg font-bold text-white mb-4">身份验证状态</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className={`p-4 rounded-lg border ${userProfile.did.verifications.biometric ? 'bg-green-500/20 border-green-500/30' : 'bg-red-500/20 border-red-500/30'}`}>
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">👆</span>
                      <span className="font-medium">生物识别</span>
                    </div>
                    <p className="text-xs text-gray-300">指纹、面部识别验证</p>
                    <div className="mt-2">
                      <span className={`px-2 py-1 rounded-full text-xs ${userProfile.did.verifications.biometric ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`}>
                        {userProfile.did.verifications.biometric ? '已验证' : '未验证'}
                      </span>
                    </div>
                  </div>

                  <div className={`p-4 rounded-lg border ${userProfile.did.verifications.social ? 'bg-green-500/20 border-green-500/30' : 'bg-red-500/20 border-red-500/30'}`}>
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">👥</span>
                      <span className="font-medium">社交验证</span>
                    </div>
                    <p className="text-xs text-gray-300">社交关系网络验证</p>
                    <div className="mt-2">
                      <span className={`px-2 py-1 rounded-full text-xs ${userProfile.did.verifications.social ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`}>
                        {userProfile.did.verifications.social ? '已验证' : '未验证'}
                      </span>
                    </div>
                  </div>

                  <div className={`p-4 rounded-lg border ${userProfile.did.verifications.professional ? 'bg-green-500/20 border-green-500/30' : 'bg-red-500/20 border-red-500/30'}`}>
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">🎓</span>
                      <span className="font-medium">专业认证</span>
                    </div>
                    <p className="text-xs text-gray-300">学历、技能认证</p>
                    <div className="mt-2">
                      <span className={`px-2 py-1 rounded-full text-xs ${userProfile.did.verifications.professional ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`}>
                        {userProfile.did.verifications.professional ? '已验证' : '未验证'}
                      </span>
                    </div>
                  </div>

                  <div className={`p-4 rounded-lg border ${userProfile.did.verifications.multiSig ? 'bg-green-500/20 border-green-500/30' : 'bg-red-500/20 border-red-500/30'}`}>
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">🔐</span>
                      <span className="font-medium">多重签名</span>
                    </div>
                    <p className="text-xs text-gray-300">多设备安全验证</p>
                    <div className="mt-2">
                      <span className={`px-2 py-1 rounded-full text-xs ${userProfile.did.verifications.multiSig ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`}>
                        {userProfile.did.verifications.multiSig ? '已验证' : '未验证'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 链上凭证 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h4 className="text-lg font-bold text-white mb-4">链上凭证</h4>
                <div className="space-y-3">
                  {userProfile.did.credentials.map((credential) => (
                    <div key={credential.id} className="bg-white/5 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">
                            {credential.type === 'education' ? '🎓' :
                             credential.type === 'skill' ? '🛠️' :
                             credential.type === 'work' ? '💼' : '📜'}
                          </span>
                          <span className="font-medium text-white">{credential.title}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {credential.onChain && (
                            <span className="px-2 py-1 bg-blue-500 rounded-full text-xs">链上验证</span>
                          )}
                          <span className="text-green-400 text-sm">✓</span>
                        </div>
                      </div>
                      <div className="text-sm text-gray-300">
                        <p>颁发机构: {credential.issuer}</p>
                        <p>验证时间: {credential.verifiedAt}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {/* Web3资产管理 */}
          {activeTab === 'web3' && (
            <motion.div
              key="web3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* 代币资产 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center space-x-2">
                  <span>💰</span>
                  <span>代币资产</span>
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-lg p-4 border border-yellow-500/30">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-2xl">🪙</span>
                      <span className="font-bold text-yellow-400">NGT</span>
                    </div>
                    <div className="text-2xl font-bold text-white">{userProfile.did.tokenBalance.toLocaleString()}</div>
                    <div className="text-sm text-yellow-300">平台治理代币</div>
                    <div className="text-xs text-gray-400 mt-1">≈ $12,840 USD</div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg p-4 border border-purple-500/30">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-2xl">🎨</span>
                      <span className="font-bold text-purple-400">CRT</span>
                    </div>
                    <div className="text-2xl font-bold text-white">8,560</div>
                    <div className="text-sm text-purple-300">创作者代币</div>
                    <div className="text-xs text-gray-400 mt-1">≈ $4,280 USD</div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-4 border border-blue-500/30">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-2xl">⚡</span>
                      <span className="font-bold text-blue-400">SKL</span>
                    </div>
                    <div className="text-2xl font-bold text-white">15,240</div>
                    <div className="text-sm text-blue-300">技能代币</div>
                    <div className="text-xs text-gray-400 mt-1">≈ $7,620 USD</div>
                  </div>

                  <div className="bg-gradient-to-r from-green-500/20 to-teal-500/20 rounded-lg p-4 border border-green-500/30">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-2xl">👥</span>
                      <span className="font-bold text-green-400">FAN</span>
                    </div>
                    <div className="text-2xl font-bold text-white">3,890</div>
                    <div className="text-sm text-green-300">粉丝代币</div>
                    <div className="text-xs text-gray-400 mt-1">≈ $1,945 USD</div>
                  </div>
                </div>
              </div>

              {/* NFT收藏 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-white flex items-center space-x-2">
                    <span>🖼️</span>
                    <span>NFT收藏</span>
                  </h3>
                  <span className="text-purple-400 font-medium">{userProfile.did.nftCount} 个</span>
                </div>
                <div className="grid grid-cols-3 gap-3">
                  {[
                    { id: 1, name: 'AI艺术 #001', rarity: 'legendary', price: '2.5 ETH' },
                    { id: 2, name: '建筑设计 #042', rarity: 'rare', price: '1.2 ETH' },
                    { id: 3, name: '元宇宙空间 #018', rarity: 'epic', price: '3.8 ETH' },
                    { id: 4, name: 'VR体验 #007', rarity: 'rare', price: '0.9 ETH' },
                    { id: 5, name: '智能合约 #156', rarity: 'common', price: '0.3 ETH' },
                    { id: 6, name: 'DeFi协议 #089', rarity: 'epic', price: '2.1 ETH' }
                  ].map((nft) => (
                    <div key={nft.id} className="bg-white/5 rounded-lg p-3 hover:bg-white/10 transition-colors">
                      <div className="aspect-square bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg mb-2 flex items-center justify-center text-2xl">
                        🎨
                      </div>
                      <h4 className="text-sm font-medium text-white truncate">{nft.name}</h4>
                      <div className="flex items-center justify-between mt-1">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          nft.rarity === 'legendary' ? 'bg-yellow-500 text-black' :
                          nft.rarity === 'epic' ? 'bg-purple-500 text-white' :
                          nft.rarity === 'rare' ? 'bg-blue-500 text-white' :
                          'bg-gray-500 text-white'
                        }`}>
                          {nft.rarity === 'legendary' ? '传说' :
                           nft.rarity === 'epic' ? '史诗' :
                           nft.rarity === 'rare' ? '稀有' : '普通'}
                        </span>
                      </div>
                      <p className="text-xs text-green-400 mt-1">{nft.price}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* DeFi投资 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center space-x-2">
                  <span>📈</span>
                  <span>DeFi投资</span>
                </h3>
                <div className="space-y-3">
                  <div className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">🏦</span>
                        <span className="font-medium text-white">流动性挖矿</span>
                      </div>
                      <span className="text-green-400 font-medium">+12.5% APY</span>
                    </div>
                    <div className="text-sm text-gray-300">
                      <p>投入: 5,000 NGT + 2.5 ETH</p>
                      <p>收益: 625 NGT (本月)</p>
                    </div>
                  </div>

                  <div className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">💰</span>
                        <span className="font-medium text-white">创作者贷款</span>
                      </div>
                      <span className="text-blue-400 font-medium">8.5% APR</span>
                    </div>
                    <div className="text-sm text-gray-300">
                      <p>借款: 10,000 USDC</p>
                      <p>抵押: 15 个NFT作品</p>
                    </div>
                  </div>

                  <div className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">🛡️</span>
                        <span className="font-medium text-white">收益保险</span>
                      </div>
                      <span className="text-purple-400 font-medium">已投保</span>
                    </div>
                    <div className="text-sm text-gray-300">
                      <p>保额: 50,000 USDC</p>
                      <p>保费: 250 USDC/年</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* DAO治理 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center space-x-2">
                  <span>🏛️</span>
                  <span>DAO治理</span>
                </h3>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400">15</div>
                    <div className="text-xs text-purple-200">参与提案</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">89%</div>
                    <div className="text-xs text-green-200">投票参与率</div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="bg-white/5 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-white">提案 #042: 平台手续费调整</span>
                      <span className="px-2 py-1 bg-green-500 rounded-full text-xs">已通过</span>
                    </div>
                    <p className="text-xs text-gray-400">您的投票: 赞成 (1,250 NGT)</p>
                  </div>
                  <div className="bg-white/5 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-white">提案 #043: 新增VR功能</span>
                      <span className="px-2 py-1 bg-yellow-500 rounded-full text-xs">投票中</span>
                    </div>
                    <p className="text-xs text-gray-400">截止时间: 2025-01-20</p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* 创作者仪表板标签页 */}
          {activeTab === 'creator' && (
            <motion.div
              key="creator"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <CreatorDashboard userId={userProfile.id} />
            </motion.div>
          )}

          {/* 积分管理标签页 */}
          {activeTab === 'points' && (
            <motion.div
              key="points"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <PointsManager
                userId={userProfile.id}
                onPointsUpdate={(points: UserPoints) => {
                  console.log('积分更新:', points)
                }}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
