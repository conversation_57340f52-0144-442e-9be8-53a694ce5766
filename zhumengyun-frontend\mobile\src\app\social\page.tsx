'use client'

import WeChatSocialPage from '../wechat-social-page'

export default function SocialPage() {
  return <WeChatSocialPage />
}

interface SocialSpace {
  id: string
  title: string
  description: string
  host: {
    name: string
    avatar: string
    verified: boolean
    followers: number
    reputation: number
  }
  stats: {
    participants: number
    likes: number
    comments: number
    shares: number
  }
  tags: string[]
  media: {
    type: 'vr-space' | 'ar-scene' | 'virtual-room' | 'metaverse-event'
    url: string
    thumbnail: string
    modelUrl?: string
  }
  spaceType: 'public' | 'private' | 'premium' | 'event'
  capacity: number
  currentUsers: number
  features: string[]
  location: string
  startTime?: string
}

export default function SocialPage() {
  const [spaces, setSpaces] = useState<SocialSpace[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'spaces' | 'chat' | 'friends' | 'dao' | 'community' | 'nft'>('spaces')
  const [messages, setMessages] = useState<any[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [selectedChatRoom, setSelectedChatRoom] = useState<string>('general')
  const [showCreateSpace, setShowCreateSpace] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const [notifications, setNotifications] = useState<any[]>([])
  const [onlineUsers, setOnlineUsers] = useState<any[]>([])
  const [userProfile, setUserProfile] = useState({
    name: '工程师·张三',
    avatar: '👨‍💻',
    level: 5,
    ngtBalance: 1250,
    reputation: 95,
    followers: 2340,
    following: 890,
    verified: true,
    did: 'did:ngt:0x1234...5678'
  })

  // 弹窗状态
  const [showEnterSpace, setShowEnterSpace] = useState(false)
  const [showFileUpload, setShowFileUpload] = useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [showCreateGroup, setShowCreateGroup] = useState(false)
  const [showBuyNFT, setShowBuyNFT] = useState(false)
  const [showCreateNFT, setShowCreateNFT] = useState(false)
  const [showCreateProposal, setShowCreateProposal] = useState(false)
  const [showDelegateVote, setShowDelegateVote] = useState(false)
  const [showCommitteeDetail, setShowCommitteeDetail] = useState(false)
  const [showSuccessToast, setShowSuccessToast] = useState(false)
  const [toastMessage, setToastMessage] = useState('')

  // 选中的项目
  const [selectedSpace, setSelectedSpace] = useState<any>(null)
  const [selectedNFT, setSelectedNFT] = useState<any>(null)
  const [selectedCommittee, setSelectedCommittee] = useState<any>(null)
  const [selectedProposal, setSelectedProposal] = useState<any>(null)

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)
  const [sendingMessage, setSendingMessage] = useState(false)

  // 聊天室数据
  const chatRooms = [
    {
      id: 'general',
      name: '综合讨论',
      icon: '💬',
      members: 1234,
      description: '工程师和创作者的综合交流空间',
      type: 'public',
      lastMessage: {
        user: '建筑师小王',
        content: '刚完成了一个AI辅助的建筑设计',
        time: '2分钟前'
      }
    },
    {
      id: 'tech',
      name: '技术讨论',
      icon: '⚙️',
      members: 890,
      description: '技术分享和问题讨论',
      type: 'public',
      lastMessage: {
        user: 'AI专家',
        content: '最新的GPT模型在工程设计中的应用',
        time: '5分钟前'
      }
    },
    {
      id: 'nft',
      name: 'NFT创作',
      icon: '🎨',
      members: 567,
      description: 'NFT创作和交易讨论',
      type: 'public',
      lastMessage: {
        user: '数字艺术家',
        content: '分享一个新的NFT作品',
        time: '10分钟前'
      }
    },
    {
      id: 'dao',
      name: 'DAO治理',
      icon: '🏛️',
      members: 456,
      description: '平台治理和提案讨论',
      type: 'premium',
      lastMessage: {
        user: '社区管理员',
        content: '新提案：降低交易手续费',
        time: '15分钟前'
      }
    }
  ]

  // 社区群组数据
  const communityGroups = [
    {
      id: 'architects',
      name: '建筑师联盟',
      icon: '🏗️',
      members: 2340,
      category: 'professional',
      description: '全球建筑师专业交流社区',
      tags: ['BIM', 'AI设计', '可持续建筑'],
      isJoined: true,
      activity: 'high'
    },
    {
      id: 'ai-creators',
      name: 'AI创作者',
      icon: '🤖',
      members: 1890,
      category: 'creative',
      description: 'AI辅助创作和艺术探索',
      tags: ['AI艺术', 'Midjourney', 'Stable Diffusion'],
      isJoined: true,
      activity: 'high'
    },
    {
      id: 'web3-builders',
      name: 'Web3建设者',
      icon: '🌐',
      members: 1567,
      category: 'technology',
      description: '区块链和Web3技术讨论',
      tags: ['DeFi', 'NFT', 'DAO'],
      isJoined: false,
      activity: 'medium'
    },
    {
      id: 'metaverse-designers',
      name: '元宇宙设计师',
      icon: '🌌',
      members: 1234,
      category: 'design',
      description: '虚拟世界设计和体验创新',
      tags: ['VR', 'AR', '3D设计'],
      isJoined: true,
      activity: 'high'
    }
  ]

  // NFT市场数据
  const nftMarketplace = [
    {
      id: 'nft-1',
      title: '未来城市建筑设计',
      creator: '建筑大师·王设计',
      price: '0.5 ETH',
      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',
      likes: 234,
      category: 'architecture',
      rarity: 'rare',
      verified: true
    },
    {
      id: 'nft-2',
      title: 'AI生成艺术作品',
      creator: 'AI艺术家·小创',
      price: '0.3 ETH',
      image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=300&fit=crop',
      likes: 456,
      category: 'ai-art',
      rarity: 'epic',
      verified: true
    },
    {
      id: 'nft-3',
      title: '智慧城市概念图',
      creator: '城市规划师',
      price: '0.8 ETH',
      image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=300&h=300&fit=crop',
      likes: 189,
      category: 'concept',
      rarity: 'legendary',
      verified: true
    }
  ]

  const mockSpaces: SocialSpace[] = [
    {
      id: '1',
      title: '未来建筑师聚会空间',
      description: '专为建筑师和设计师打造的虚拟聚会空间，支持3D模型展示、实时协作设计和专业交流。',
      host: {
        name: '建筑大师·王设计',
        avatar: '🏛️',
        verified: true,
        followers: 15600,
        reputation: 95
      },
      stats: {
        participants: 234,
        likes: 1890,
        comments: 456,
        shares: 123
      },
      tags: ['建筑设计', 'VR协作', '专业交流', '3D展示', '设计师社区'],
      media: {
        type: 'vr-space',
        url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',
        thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',
        modelUrl: '/models/architect-space.glb'
      },
      spaceType: 'public',
      capacity: 500,
      currentUsers: 234,
      features: ['3D模型展示', '语音聊天', '屏幕共享', 'AI助手', '实时协作'],
      location: '虚拟建筑学院',
      startTime: '2025-01-15T19:00:00Z'
    },
    {
      id: '2',
      title: 'AI创作者元宇宙派对',
      description: 'AI艺术家和创作者的专属聚会空间，展示最新AI生成艺术作品，交流创作技巧和商业合作。',
      host: {
        name: 'AI艺术家·小创',
        avatar: '🎨',
        verified: true,
        followers: 28900,
        reputation: 88
      },
      stats: {
        participants: 567,
        likes: 3450,
        comments: 890,
        shares: 234
      },
      tags: ['AI艺术', '创作者经济', 'NFT展示', '商业合作', '技术交流'],
      media: {
        type: 'metaverse-event',
        url: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',
        thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',
        modelUrl: '/models/creator-party.glb'
      },
      spaceType: 'event',
      capacity: 1000,
      currentUsers: 567,
      features: ['NFT画廊', '音乐DJ', '互动游戏', '商务洽谈', 'AI生成艺术'],
      location: '创作者元宇宙中心',
      startTime: '2025-01-15T20:00:00Z'
    },
    {
      id: '3',
      title: '工程师技术分享会',
      description: '全球工程师的技术分享和学习空间，讨论最新技术趋势、开源项目和职业发展。',
      host: {
        name: '技术专家·李工程师',
        avatar: '⚙️',
        verified: true,
        followers: 45200,
        reputation: 92
      },
      stats: {
        participants: 890,
        likes: 5670,
        comments: 1234,
        shares: 456
      },
      tags: ['技术分享', '开源项目', '职业发展', '编程技术', '工程师社区'],
      media: {
        type: 'virtual-room',
        url: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',
        thumbnail: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',
        modelUrl: '/models/tech-meetup.glb'
      },
      spaceType: 'public',
      capacity: 2000,
      currentUsers: 890,
      features: ['代码演示', '技术讲座', '项目展示', '招聘信息', '导师指导'],
      location: '全球技术中心',
      startTime: '2025-01-15T21:00:00Z'
    }
  ]

  useEffect(() => {
    setLoading(true)
    setTimeout(() => {
      setSpaces(mockSpaces)
      setLoading(false)
    }, 1000)
  }, [])

  // 显示成功提示
  const showToast = (message: string) => {
    setToastMessage(message)
    setShowSuccessToast(true)
    setTimeout(() => setShowSuccessToast(false), 3000)
  }

  // 进入虚拟空间
  const handleEnterSpace = (space: any) => {
    setSelectedSpace(space)
    setShowEnterSpace(true)
  }

  const confirmEnterSpace = () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      setShowEnterSpace(false)
      showToast(`成功进入 ${selectedSpace?.title}`)
    }, 2000)
  }

  // 发送消息
  const handleSendMessage = () => {
    if (!newMessage.trim()) return

    setSendingMessage(true)
    setTimeout(() => {
      setSendingMessage(false)
      showToast('消息发送成功')
      setNewMessage('')
    }, 1000)
  }

  // 文件上传
  const handleFileUpload = () => {
    setShowFileUpload(true)
  }

  const confirmFileUpload = (files: FileList) => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      setShowFileUpload(false)
      showToast(`成功上传 ${files.length} 个文件`)
    }, 2000)
  }

  // 表情选择
  const handleEmojiSelect = (emoji: string) => {
    setNewMessage(prev => prev + emoji)
    setShowEmojiPicker(false)
  }

  // 群组操作
  const handleJoinGroup = (groupId: string) => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      showToast('成功加入群组')
    }, 1500)
  }

  const handleLeaveGroup = (groupId: string) => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      showToast('已退出群组')
    }, 1500)
  }

  const handleEnterGroup = (groupId: string) => {
    showToast('正在进入群组聊天...')
  }

  const handleGroupSettings = (groupId: string) => {
    showToast('群组设置功能开发中...')
  }

  const handleCreateGroup = () => {
    setShowCreateGroup(true)
  }

  const confirmCreateGroup = (groupData: any) => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      setShowCreateGroup(false)
      showToast('群组创建成功')
    }, 2000)
  }

  // NFT操作
  const handleBuyNFT = (nft: any) => {
    setSelectedNFT(nft)
    setShowBuyNFT(true)
  }

  const confirmBuyNFT = () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      setShowBuyNFT(false)
      showToast(`成功购买 ${selectedNFT?.title}`)
    }, 2000)
  }

  const handleCreateNFT = () => {
    setShowCreateNFT(true)
  }

  const confirmCreateNFT = (nftData: any) => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      setShowCreateNFT(false)
      showToast('NFT创建成功')
    }, 3000)
  }

  const handleNFTCategory = (category: string) => {
    showToast(`正在浏览 ${category} 分类...`)
  }

  // DAO治理操作
  const handleVote = (proposalId: string, vote: 'for' | 'against') => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      showToast(`投票成功：${vote === 'for' ? '赞成' : '反对'}`)
    }, 1500)
  }

  const handleCreateProposal = () => {
    setShowCreateProposal(true)
  }

  const confirmCreateProposal = (proposalData: any) => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      setShowCreateProposal(false)
      showToast('提案创建成功')
    }, 2000)
  }

  const handleDelegateVote = () => {
    setShowDelegateVote(true)
  }

  const confirmDelegateVote = (delegateData: any) => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      setShowDelegateVote(false)
      showToast('投票权委托成功')
    }, 1500)
  }

  const handleCommitteeDetail = (committee: any) => {
    setSelectedCommittee(committee)
    setShowCommitteeDetail(true)
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
    if (num >= 1000) return (num / 1000).toFixed(1) + 'k'
    return num.toString()
  }

  const getSpaceTypeColor = (type: string) => {
    switch (type) {
      case 'public': return 'bg-green-500'
      case 'private': return 'bg-red-500'
      case 'premium': return 'bg-yellow-500'
      case 'event': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  const getSpaceTypeText = (type: string) => {
    switch (type) {
      case 'public': return '公开'
      case 'private': return '私密'
      case 'premium': return '高级'
      case 'event': return '活动'
      default: return '未知'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-teal-500 rounded-2xl flex items-center justify-center mb-4 mx-auto animate-pulse">
            <span className="text-white font-bold text-2xl">🚀</span>
          </div>
          <p className="text-green-300 text-lg mb-2">社交元宇宙</p>
          <p className="text-white text-sm">连接虚拟空间中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 text-white">
      {/* 社交元宇宙界面 */}
      <div className="relative z-10 flex flex-col h-screen">
        {/* 顶部导航 */}
        <div className="bg-black/20 backdrop-blur-lg border-b border-white/10 p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-teal-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">🚀</span>
              </div>
              <div>
                <h1 className="text-lg font-bold">社交元宇宙</h1>
                <p className="text-xs text-green-300">虚拟空间 • 实时聊天 • 社交网络</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1 bg-white/20 rounded-full px-2 py-1">
                <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                <span className="text-xs">1.2k 在线</span>
              </div>
            </div>
          </div>

          {/* 标签切换 */}
          <div className="flex space-x-1 bg-white/10 rounded-lg p-1 overflow-x-auto">
            {[
              { key: 'spaces', label: '虚拟空间', icon: '🌌' },
              { key: 'chat', label: '实时聊天', icon: '💬' },
              { key: 'community', label: '社区群组', icon: '👥' },
              { key: 'friends', label: '好友动态', icon: '🤝' },
              { key: 'nft', label: 'NFT市场', icon: '🎨' },
              { key: 'dao', label: 'DAO治理', icon: '🏛️' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex-shrink-0 flex items-center justify-center space-x-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.key
                    ? 'bg-green-500 text-white'
                    : 'text-white/70 hover:text-white'
                }`}
              >
                <span>{tab.icon}</span>
                <span className="whitespace-nowrap">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 overflow-hidden">
          {/* 虚拟空间标签页 */}
          {activeTab === 'spaces' && (
            <div className="h-full overflow-y-auto p-4 space-y-4">
              {spaces.map((space, index) => (
                <motion.div
                  key={space.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20"
                >
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl">
                          {space.host.avatar}
                        </div>
                        <div>
                          <h3 className="font-bold text-white">{space.title}</h3>
                          <p className="text-xs text-green-300">{space.host.name}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-1 text-xs text-green-400">
                          <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                          <span>{space.currentUsers}/{space.capacity}</span>
                        </div>
                        <span className={`px-2 py-1 ${getSpaceTypeColor(space.spaceType)} rounded-full text-xs font-medium mt-1 inline-block`}>
                          {getSpaceTypeText(space.spaceType)}
                        </span>
                      </div>
                    </div>

                    <p className="text-sm text-gray-300 mb-3">{space.description}</p>

                    <div className="space-y-3">
                      <div className="flex space-x-2">
                        {space.features.slice(0, 2).map((feature, idx) => (
                          <span key={idx} className="px-2 py-1 bg-green-500/20 rounded text-xs">
                            {feature}
                          </span>
                        ))}
                      </div>

                      {/* 统一交互按钮设计 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-6">
                          <button className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors">
                            <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                              👍
                            </div>
                            <span className="text-xs">121</span>
                          </button>

                          <button className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors">
                            <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                              🔄
                            </div>
                            <span className="text-xs">33</span>
                          </button>

                          <button className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors">
                            <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                              ❤️
                            </div>
                            <span className="text-xs">45</span>
                          </button>

                          <button className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors">
                            <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                              💬
                            </div>
                            <span className="text-xs">67</span>
                          </button>
                        </div>

                        <button
                          onClick={() => handleEnterSpace(space)}
                          className="px-4 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium hover:from-green-600 hover:to-teal-600 transition-colors"
                        >
                          🚀 进入空间
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* 实时聊天标签页 */}
          {activeTab === 'chat' && (
            <div className="h-full flex flex-col">
              {/* 聊天室选择 */}
              <div className="p-4 border-b border-white/10">
                <div className="flex space-x-2 overflow-x-auto">
                  {chatRooms.map((room) => (
                    <button
                      key={room.id}
                      onClick={() => setSelectedChatRoom(room.id)}
                      className={`flex-shrink-0 flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors ${
                        selectedChatRoom === room.id
                          ? 'bg-green-500 border-green-500 text-white'
                          : 'bg-white/10 border-white/20 text-gray-300 hover:border-white/40'
                      }`}
                    >
                      <span>{room.icon}</span>
                      <span className="text-sm font-medium">{room.name}</span>
                      <span className="text-xs bg-white/20 px-1 rounded">{room.members}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* 聊天消息区域 */}
              <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {/* 聊天室信息 */}
                <div className="text-center py-4">
                  <div className="text-2xl mb-2">{chatRooms.find(r => r.id === selectedChatRoom)?.icon}</div>
                  <h3 className="font-bold text-white">{chatRooms.find(r => r.id === selectedChatRoom)?.name}</h3>
                  <p className="text-sm text-gray-400">{chatRooms.find(r => r.id === selectedChatRoom)?.description}</p>
                  <p className="text-xs text-green-400 mt-1">{chatRooms.find(r => r.id === selectedChatRoom)?.members} 成员在线</p>
                </div>

                {/* 消息列表 */}
                {[
                  { id: 1, user: '🏗️ 建筑师小王', message: '刚完成了一个AI辅助的建筑设计，效果很棒！', time: '2分钟前', isMe: false, avatar: '🏗️' },
                  { id: 2, user: '我', message: '能分享一下设计图吗？很想看看AI的效果', time: '1分钟前', isMe: true, avatar: '👨‍💻' },
                  { id: 3, user: '🎨 AI艺术家', message: '我也在用AI创作NFT，最近很火呢', time: '30秒前', isMe: false, avatar: '🎨' },
                  { id: 4, user: '🌐 Web3开发者', message: '有人想合作开发DApp吗？', time: '刚刚', isMe: false, avatar: '🌐' },
                ].map((msg) => (
                  <div key={msg.id} className={`flex ${msg.isMe ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] flex ${msg.isMe ? 'flex-row-reverse' : 'flex-row'} items-start space-x-2`}>
                      {!msg.isMe && (
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-sm">
                          {msg.avatar}
                        </div>
                      )}
                      <div className={`${msg.isMe ? 'bg-green-500 mr-2' : 'bg-white/10'} rounded-lg p-3`}>
                        {!msg.isMe && <p className="text-xs text-green-300 mb-1">{msg.user}</p>}
                        <p className="text-sm text-white">{msg.message}</p>
                        <p className="text-xs text-gray-400 mt-1">{msg.time}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 消息输入框 */}
              <div className="p-4 pb-20 border-t border-white/10">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleFileUpload}
                    className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                  >
                    📎
                  </button>
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder={`在 ${chatRooms.find(r => r.id === selectedChatRoom)?.name} 中发消息...`}
                    className="flex-1 bg-white/10 text-white px-4 py-3 rounded-full border border-white/20 focus:border-green-500 focus:outline-none"
                  />
                  <button
                    onClick={() => setShowEmojiPicker(true)}
                    className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                  >
                    😊
                  </button>
                  <button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim() || sendingMessage}
                    className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
                      !newMessage.trim() || sendingMessage
                        ? 'bg-gray-600 cursor-not-allowed'
                        : 'bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600'
                    }`}
                  >
                    <span className="text-xl">{sendingMessage ? '⏳' : '🚀'}</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 社区群组标签页 */}
          {activeTab === 'community' && (
            <div className="h-full overflow-y-auto p-4 space-y-4">
              {/* 推荐群组 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center space-x-2">
                  <span>⭐</span>
                  <span>推荐群组</span>
                </h3>
                <div className="space-y-3">
                  {communityGroups.map((group) => (
                    <div key={group.id} className="bg-white/5 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl">
                            {group.icon}
                          </div>
                          <div>
                            <h4 className="font-bold text-white">{group.name}</h4>
                            <p className="text-xs text-green-300">{group.members.toLocaleString()} 成员</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${
                            group.activity === 'high' ? 'bg-green-400' :
                            group.activity === 'medium' ? 'bg-yellow-400' : 'bg-gray-400'
                          }`}></div>
                          <span className="text-xs text-gray-400">
                            {group.activity === 'high' ? '活跃' :
                             group.activity === 'medium' ? '一般' : '较少'}
                          </span>
                        </div>
                      </div>

                      <p className="text-sm text-gray-300 mb-3">{group.description}</p>

                      <div className="flex flex-wrap gap-2 mb-3">
                        {group.tags.map((tag, idx) => (
                          <span key={idx} className="px-2 py-1 bg-green-500/20 rounded text-xs text-green-300">
                            #{tag}
                          </span>
                        ))}
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-400 capitalize">{group.category}</span>
                        <button
                          onClick={() => group.isJoined ? handleLeaveGroup(group.id) : handleJoinGroup(group.id)}
                          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                            group.isJoined
                              ? 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                              : 'bg-gradient-to-r from-green-500 to-teal-500 text-white hover:from-green-600 hover:to-teal-600'
                          }`}
                        >
                          {group.isJoined ? '已加入' : '加入群组'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 我的群组 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center space-x-2">
                  <span>👥</span>
                  <span>我的群组</span>
                </h3>
                <div className="space-y-3">
                  {communityGroups.filter(g => g.isJoined).map((group) => (
                    <div key={group.id} className="bg-white/5 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                            {group.icon}
                          </div>
                          <div>
                            <h4 className="font-medium text-white">{group.name}</h4>
                            <p className="text-xs text-gray-400">{group.members.toLocaleString()} 成员</p>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEnterGroup(group.id)}
                            className="px-3 py-1 bg-green-500 rounded-lg text-xs font-medium hover:bg-green-600 transition-colors"
                          >
                            进入
                          </button>
                          <button
                            onClick={() => handleGroupSettings(group.id)}
                            className="px-3 py-1 bg-white/10 rounded-lg text-xs font-medium text-gray-400 hover:bg-white/20 hover:text-white transition-colors"
                          >
                            设置
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 创建群组 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center space-x-2">
                  <span>➕</span>
                  <span>创建群组</span>
                </h3>
                <p className="text-sm text-gray-300 mb-4">
                  创建专属的专业群组，聚集志同道合的伙伴，共同探讨技术和创意。
                </p>
                <button
                  onClick={handleCreateGroup}
                  className="w-full py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-white font-medium hover:from-green-600 hover:to-teal-600 transition-colors"
                >
                  🚀 创建新群组
                </button>
              </div>
            </div>
          )}

          {/* 好友动态标签页 */}
          {activeTab === 'friends' && (
            <div className="h-full overflow-y-auto p-4 space-y-4">
              {[
                { id: 1, user: '🏗️ 建筑师小王', action: '发布了新的BIM模型', content: 'AI驱动的智慧建筑设计', time: '5分钟前', likes: 23 },
                { id: 2, user: '🎨 AI艺术家', action: '创建了NFT作品', content: '赛博朋克风格的未来城市', time: '15分钟前', likes: 45 },
                { id: 3, user: '🚀 元宇宙设计师', action: '加入了虚拟空间', content: '建筑师专业交流会', time: '30分钟前', likes: 12 },
              ].map((activity) => (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20"
                >
                  <div className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center">
                      <span>{activity.user.split(' ')[0]}</span>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-white">
                        <span className="font-medium">{activity.user.split(' ')[1]}</span>
                        <span className="text-gray-400"> {activity.action}</span>
                      </p>
                      <p className="text-sm text-green-300 mt-1">{activity.content}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                        <span>{activity.time}</span>
                        <button className="flex items-center space-x-1 text-green-400">
                          <span>❤️</span>
                          <span>{activity.likes}</span>
                        </button>
                        <button className="text-gray-400">💬 回复</button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* NFT市场标签页 */}
          {activeTab === 'nft' && (
            <div className="h-full overflow-y-auto p-4 space-y-4">
              {/* NFT市场概览 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center space-x-2">
                  <span>🎨</span>
                  <span>NFT市场</span>
                </h3>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-xl font-bold text-purple-400">12,456</div>
                    <div className="text-xs text-purple-200">总NFT数量</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-green-400">2,890</div>
                    <div className="text-xs text-green-200">活跃创作者</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-blue-400">156.8 ETH</div>
                    <div className="text-xs text-blue-200">24h交易量</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-yellow-400">0.45 ETH</div>
                    <div className="text-xs text-yellow-200">平均价格</div>
                  </div>
                </div>
              </div>

              {/* 热门NFT */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <h4 className="text-lg font-bold text-white mb-4">🔥 热门NFT</h4>
                <div className="space-y-4">
                  {nftMarketplace.map((nft) => (
                    <div key={nft.id} className="bg-white/5 rounded-lg overflow-hidden">
                      <div className="flex">
                        <div className="w-20 h-20 bg-gray-600 rounded-lg overflow-hidden">
                          <img
                            src={nft.image}
                            alt={nft.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1 p-3">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="font-medium text-white text-sm">{nft.title}</h5>
                            <div className="flex items-center space-x-1">
                              {nft.verified && <span className="text-blue-400">✓</span>}
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                nft.rarity === 'legendary' ? 'bg-yellow-500' :
                                nft.rarity === 'epic' ? 'bg-purple-500' :
                                nft.rarity === 'rare' ? 'bg-blue-500' : 'bg-gray-500'
                              }`}>
                                {nft.rarity}
                              </span>
                            </div>
                          </div>
                          <p className="text-xs text-gray-400 mb-2">{nft.creator}</p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <span className="text-green-400 font-bold text-sm">{nft.price}</span>
                              <span className="text-xs text-gray-400">≈ ${(parseFloat(nft.price) * 2500).toFixed(0)}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                              <div className="flex items-center space-x-1 text-xs text-gray-400">
                                <span>❤️</span>
                                <span>{nft.likes}</span>
                              </div>
                              <button
                                onClick={() => handleBuyNFT(nft)}
                                className="px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-xs font-medium hover:from-purple-600 hover:to-pink-600 transition-colors"
                              >
                                购买
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 我的NFT */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <h4 className="text-lg font-bold text-white mb-4">🖼️ 我的NFT</h4>
                <div className="grid grid-cols-2 gap-3">
                  {[
                    { id: 'my-1', title: '工程设计图', price: '0.2 ETH', status: 'owned' },
                    { id: 'my-2', title: 'AI生成艺术', price: '0.15 ETH', status: 'selling' },
                  ].map((nft) => (
                    <div key={nft.id} className="bg-white/5 rounded-lg p-3">
                      <div className="aspect-square bg-gray-600 rounded-lg mb-2"></div>
                      <h5 className="font-medium text-white text-sm mb-1">{nft.title}</h5>
                      <div className="flex items-center justify-between">
                        <span className="text-green-400 text-xs font-bold">{nft.price}</span>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          nft.status === 'owned' ? 'bg-blue-500' : 'bg-green-500'
                        }`}>
                          {nft.status === 'owned' ? '持有' : '出售中'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
                <button
                  onClick={handleCreateNFT}
                  className="w-full mt-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-white font-medium hover:from-purple-600 hover:to-pink-600 transition-colors"
                >
                  🎨 创建NFT
                </button>
              </div>

              {/* NFT分类 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <h4 className="text-lg font-bold text-white mb-4">📂 NFT分类</h4>
                <div className="grid grid-cols-2 gap-3">
                  {[
                    { name: '建筑设计', icon: '🏗️', count: 1234 },
                    { name: 'AI艺术', icon: '🤖', count: 2890 },
                    { name: '概念设计', icon: '💡', count: 567 },
                    { name: '3D模型', icon: '🎯', count: 890 }
                  ].map((category) => (
                    <button
                      key={category.name}
                      onClick={() => handleNFTCategory(category.name)}
                      className="bg-white/5 rounded-lg p-3 text-left hover:bg-white/10 transition-colors"
                    >
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-lg">{category.icon}</span>
                        <span className="font-medium text-white text-sm">{category.name}</span>
                      </div>
                      <p className="text-xs text-gray-400">{category.count} 个NFT</p>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* DAO治理标签页 */}
          {activeTab === 'dao' && (
            <div className="h-full overflow-y-auto p-4 space-y-4">
              {/* DAO概览 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center space-x-2">
                  <span>🏛️</span>
                  <span>NextGen DAO</span>
                </h3>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">25,680</div>
                    <div className="text-xs text-green-200">NGT持有者</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-400">156</div>
                    <div className="text-xs text-blue-200">活跃提案</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400">89%</div>
                    <div className="text-xs text-purple-200">参与率</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-400">4.8M</div>
                    <div className="text-xs text-yellow-200">总投票权</div>
                  </div>
                </div>
                <div className="bg-white/5 rounded-lg p-3">
                  <p className="text-sm text-gray-300">
                    NextGen DAO是一个去中心化自治组织，由社区成员共同治理平台的发展方向。
                    持有NGT代币即可参与提案投票，共同决定平台的未来。
                  </p>
                </div>
              </div>

              {/* 活跃提案 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h4 className="text-lg font-bold text-white mb-4">活跃提案</h4>
                <div className="space-y-3">
                  {[
                    {
                      id: 'prop-042',
                      title: '降低平台交易手续费至2%',
                      description: '建议将NFT交易手续费从3%降低至2%，以提高平台竞争力',
                      status: 'voting',
                      votes: { for: 15420, against: 3280 },
                      endTime: '2025-01-20',
                      category: 'economic'
                    },
                    {
                      id: 'prop-043',
                      title: '新增VR虚拟展厅功能',
                      description: '为创作者提供VR虚拟展厅，展示NFT作品集',
                      status: 'voting',
                      votes: { for: 12890, against: 1560 },
                      endTime: '2025-01-22',
                      category: 'feature'
                    },
                    {
                      id: 'prop-044',
                      title: '建立创作者扶持基金',
                      description: '从平台收益中拨出10%建立创作者扶持基金',
                      status: 'passed',
                      votes: { for: 18920, against: 2340 },
                      endTime: '2025-01-15',
                      category: 'community'
                    }
                  ].map((proposal) => (
                    <div key={proposal.id} className="bg-white/5 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium text-white">{proposal.title}</h5>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          proposal.status === 'voting' ? 'bg-yellow-500' :
                          proposal.status === 'passed' ? 'bg-green-500' :
                          'bg-red-500'
                        }`}>
                          {proposal.status === 'voting' ? '投票中' :
                           proposal.status === 'passed' ? '已通过' : '未通过'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-300 mb-3">{proposal.description}</p>

                      {/* 投票进度 */}
                      <div className="mb-3">
                        <div className="flex items-center justify-between text-xs mb-1">
                          <span className="text-green-400">赞成: {proposal.votes.for.toLocaleString()}</span>
                          <span className="text-red-400">反对: {proposal.votes.against.toLocaleString()}</span>
                        </div>
                        <div className="w-full bg-white/20 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full"
                            style={{
                              width: `${(proposal.votes.for / (proposal.votes.for + proposal.votes.against)) * 100}%`
                            }}
                          ></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-400">
                          {proposal.status === 'voting' ? `截止: ${proposal.endTime}` : `结束: ${proposal.endTime}`}
                        </span>
                        {proposal.status === 'voting' && (
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleVote(proposal.id, 'for')}
                              className="px-3 py-1 bg-green-500 rounded-lg text-xs font-medium hover:bg-green-600 transition-colors"
                            >
                              赞成
                            </button>
                            <button
                              onClick={() => handleVote(proposal.id, 'against')}
                              className="px-3 py-1 bg-red-500 rounded-lg text-xs font-medium hover:bg-red-600 transition-colors"
                            >
                              反对
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 专业委员会 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h4 className="text-lg font-bold text-white mb-4">专业委员会</h4>
                <div className="grid grid-cols-2 gap-3">
                  {[
                    { name: '技术委员会', icon: '⚙️', members: 12, description: '技术路线制定' },
                    { name: '内容委员会', icon: '📝', members: 8, description: '内容质量监管' },
                    { name: '经济委员会', icon: '💰', members: 10, description: '代币经济设计' },
                    { name: '仲裁委员会', icon: '⚖️', members: 6, description: '争议处理' }
                  ].map((committee) => (
                    <div key={committee.name} className="bg-white/5 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-lg">{committee.icon}</span>
                        <span className="font-medium text-white text-sm">{committee.name}</span>
                      </div>
                      <p className="text-xs text-gray-400 mb-2">{committee.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-green-400">{committee.members} 成员</span>
                        <button
                          onClick={() => handleCommitteeDetail(committee)}
                          className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          查看详情
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 我的治理参与 */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h4 className="text-lg font-bold text-white mb-4">我的治理参与</h4>
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-xl font-bold text-purple-400">1,250</div>
                    <div className="text-xs text-purple-200">投票权重</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-green-400">15</div>
                    <div className="text-xs text-green-200">参与提案</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-yellow-400">89%</div>
                    <div className="text-xs text-yellow-200">参与率</div>
                  </div>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={handleCreateProposal}
                    className="flex-1 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium hover:from-green-600 hover:to-teal-600 transition-colors"
                  >
                    创建提案
                  </button>
                  <button
                    onClick={handleDelegateVote}
                    className="flex-1 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-purple-600 transition-colors"
                  >
                    委托投票
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 成功提示 */}
      {showSuccessToast && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <span>✅</span>
            <span>{toastMessage}</span>
          </div>
        </div>
      )}

      {/* 进入虚拟空间弹窗 */}
      {showEnterSpace && selectedSpace && (
        <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20">
            <div className="text-center mb-6">
              <div className="text-4xl mb-3">{selectedSpace.host.avatar}</div>
              <h3 className="text-xl font-bold text-white mb-2">{selectedSpace.title}</h3>
              <p className="text-gray-400 text-sm">{selectedSpace.description}</p>
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">当前人数</span>
                <span className="text-green-400">{selectedSpace.currentUsers}/{selectedSpace.capacity}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">空间类型</span>
                <span className="text-blue-400">{getSpaceTypeText(selectedSpace.spaceType)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">主要功能</span>
                <span className="text-purple-400">{selectedSpace.features.slice(0, 2).join(', ')}</span>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowEnterSpace(false)}
                className="flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors"
              >
                取消
              </button>
              <button
                onClick={confirmEnterSpace}
                disabled={isLoading}
                className="flex-1 py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl text-white font-medium hover:from-green-600 hover:to-teal-600 transition-colors disabled:opacity-50"
              >
                {isLoading ? '进入中...' : '确认进入'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 表情选择器 */}
      {showEmojiPicker && (
        <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">选择表情</h3>
              <button
                onClick={() => setShowEmojiPicker(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>

            <div className="grid grid-cols-6 gap-3 mb-4">
              {['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩'].map((emoji) => (
                <button
                  key={emoji}
                  onClick={() => handleEmojiSelect(emoji)}
                  className="text-2xl p-2 hover:bg-white/10 rounded-lg transition-colors"
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 文件上传弹窗 */}
      {showFileUpload && (
        <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">上传文件</h3>
              <button
                onClick={() => setShowFileUpload(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>

            <div className="border-2 border-dashed border-white/20 rounded-lg p-8 text-center mb-4">
              <div className="text-4xl mb-4">📁</div>
              <p className="text-gray-400 mb-2">点击或拖拽上传文件</p>
              <p className="text-xs text-gray-500">支持图片、视频、文档等格式</p>
              <input
                type="file"
                multiple
                className="hidden"
                onChange={(e) => e.target.files && confirmFileUpload(e.target.files)}
              />
            </div>

            <button
              onClick={() => (document.querySelector('input[type="file"]') as HTMLInputElement)?.click()}
              className="w-full py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl text-white font-medium hover:from-green-600 hover:to-teal-600 transition-colors"
            >
              选择文件
            </button>
          </div>
        </div>
      )}

    </div>
  )
}
