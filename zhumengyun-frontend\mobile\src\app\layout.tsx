import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Toaster } from 'react-hot-toast'
import { Suspense } from 'react'
import BottomNavigation from '@/components/BottomNavigation'
import { UIProvider } from '@/contexts/UIContext'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'NextGen 2025 - AI原生数字生活操作系统',
  description: '重新定义人类与数字世界的交互方式，构建AI原生的未来数字生活生态系统。',
}

// AI原生加载组件
function AILoading() {
  return (
    <div className="fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center z-50">
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-2xl flex items-center justify-center mb-6 mx-auto animate-pulse shadow-2xl">
          <span className="text-white font-bold text-2xl">🧠</span>
        </div>
        <h2 className="text-2xl font-bold text-white mb-2 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
          NextGen 2025
        </h2>
        <p className="text-sm text-cyan-300 animate-pulse">AI原生系统启动中...</p>
        <div className="mt-4 flex justify-center space-x-1">
          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
          <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
        </div>
      </div>
    </div>
  )
}



export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <meta name="theme-color" content="#3b82f6" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={`${inter.className} bg-black text-white antialiased`}>
        <UIProvider>
          <Suspense fallback={<AILoading />}>
            <div className="min-h-screen flex flex-col">
              <main className="flex-1 pb-20 overflow-hidden">
                {children}
              </main>
              <BottomNavigation />
            </div>
          </Suspense>
        </UIProvider>

        <Toaster
          position="top-center"
          toastOptions={{
            duration: 3000,
            style: {
              background: '#363636',
              color: '#fff',
              fontSize: '14px',
              borderRadius: '8px',
              padding: '12px 16px',
            },
          }}
        />
      </body>
    </html>
  )
}
