"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/social/page",{

/***/ "(app-pages-browser)/./src/app/social/page.tsx":
/*!*********************************!*\
  !*** ./src/app/social/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SocialPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SocialPage() {\n    var _chatRooms_find, _chatRooms_find1, _chatRooms_find2, _chatRooms_find3, _chatRooms_find4;\n    _s();\n    const [spaces, setSpaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('spaces');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedChatRoom, setSelectedChatRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [showCreateSpace, setShowCreateSpace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProfile, setShowProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [onlineUsers, setOnlineUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '工程师·张三',\n        avatar: '👨‍💻',\n        level: 5,\n        ngtBalance: 1250,\n        reputation: 95,\n        followers: 2340,\n        following: 890,\n        verified: true,\n        did: 'did:ngt:0x1234...5678'\n    });\n    // 弹窗状态\n    const [showEnterSpace, setShowEnterSpace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFileUpload, setShowFileUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateGroup, setShowCreateGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBuyNFT, setShowBuyNFT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateNFT, setShowCreateNFT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateProposal, setShowCreateProposal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDelegateVote, setShowDelegateVote] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommitteeDetail, setShowCommitteeDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccessToast, setShowSuccessToast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toastMessage, setToastMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 选中的项目\n    const [selectedSpace, setSelectedSpace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedNFT, setSelectedNFT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCommittee, setSelectedCommittee] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProposal, setSelectedProposal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 加载状态\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sendingMessage, setSendingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 聊天室数据\n    const chatRooms = [\n        {\n            id: 'general',\n            name: '综合讨论',\n            icon: '💬',\n            members: 1234,\n            description: '工程师和创作者的综合交流空间',\n            type: 'public',\n            lastMessage: {\n                user: '建筑师小王',\n                content: '刚完成了一个AI辅助的建筑设计',\n                time: '2分钟前'\n            }\n        },\n        {\n            id: 'tech',\n            name: '技术讨论',\n            icon: '⚙️',\n            members: 890,\n            description: '技术分享和问题讨论',\n            type: 'public',\n            lastMessage: {\n                user: 'AI专家',\n                content: '最新的GPT模型在工程设计中的应用',\n                time: '5分钟前'\n            }\n        },\n        {\n            id: 'nft',\n            name: 'NFT创作',\n            icon: '🎨',\n            members: 567,\n            description: 'NFT创作和交易讨论',\n            type: 'public',\n            lastMessage: {\n                user: '数字艺术家',\n                content: '分享一个新的NFT作品',\n                time: '10分钟前'\n            }\n        },\n        {\n            id: 'dao',\n            name: 'DAO治理',\n            icon: '🏛️',\n            members: 456,\n            description: '平台治理和提案讨论',\n            type: 'premium',\n            lastMessage: {\n                user: '社区管理员',\n                content: '新提案：降低交易手续费',\n                time: '15分钟前'\n            }\n        }\n    ];\n    // 社区群组数据\n    const communityGroups = [\n        {\n            id: 'architects',\n            name: '建筑师联盟',\n            icon: '🏗️',\n            members: 2340,\n            category: 'professional',\n            description: '全球建筑师专业交流社区',\n            tags: [\n                'BIM',\n                'AI设计',\n                '可持续建筑'\n            ],\n            isJoined: true,\n            activity: 'high'\n        },\n        {\n            id: 'ai-creators',\n            name: 'AI创作者',\n            icon: '🤖',\n            members: 1890,\n            category: 'creative',\n            description: 'AI辅助创作和艺术探索',\n            tags: [\n                'AI艺术',\n                'Midjourney',\n                'Stable Diffusion'\n            ],\n            isJoined: true,\n            activity: 'high'\n        },\n        {\n            id: 'web3-builders',\n            name: 'Web3建设者',\n            icon: '🌐',\n            members: 1567,\n            category: 'technology',\n            description: '区块链和Web3技术讨论',\n            tags: [\n                'DeFi',\n                'NFT',\n                'DAO'\n            ],\n            isJoined: false,\n            activity: 'medium'\n        },\n        {\n            id: 'metaverse-designers',\n            name: '元宇宙设计师',\n            icon: '🌌',\n            members: 1234,\n            category: 'design',\n            description: '虚拟世界设计和体验创新',\n            tags: [\n                'VR',\n                'AR',\n                '3D设计'\n            ],\n            isJoined: true,\n            activity: 'high'\n        }\n    ];\n    // NFT市场数据\n    const nftMarketplace = [\n        {\n            id: 'nft-1',\n            title: '未来城市建筑设计',\n            creator: '建筑大师·王设计',\n            price: '0.5 ETH',\n            image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',\n            likes: 234,\n            category: 'architecture',\n            rarity: 'rare',\n            verified: true\n        },\n        {\n            id: 'nft-2',\n            title: 'AI生成艺术作品',\n            creator: 'AI艺术家·小创',\n            price: '0.3 ETH',\n            image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=300&fit=crop',\n            likes: 456,\n            category: 'ai-art',\n            rarity: 'epic',\n            verified: true\n        },\n        {\n            id: 'nft-3',\n            title: '智慧城市概念图',\n            creator: '城市规划师',\n            price: '0.8 ETH',\n            image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=300&h=300&fit=crop',\n            likes: 189,\n            category: 'concept',\n            rarity: 'legendary',\n            verified: true\n        }\n    ];\n    const mockSpaces = [\n        {\n            id: '1',\n            title: '未来建筑师聚会空间',\n            description: '专为建筑师和设计师打造的虚拟聚会空间，支持3D模型展示、实时协作设计和专业交流。',\n            host: {\n                name: '建筑大师·王设计',\n                avatar: '🏛️',\n                verified: true,\n                followers: 15600,\n                reputation: 95\n            },\n            stats: {\n                participants: 234,\n                likes: 1890,\n                comments: 456,\n                shares: 123\n            },\n            tags: [\n                '建筑设计',\n                'VR协作',\n                '专业交流',\n                '3D展示',\n                '设计师社区'\n            ],\n            media: {\n                type: 'vr-space',\n                url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=800&fit=crop',\n                modelUrl: '/models/architect-space.glb'\n            },\n            spaceType: 'public',\n            capacity: 500,\n            currentUsers: 234,\n            features: [\n                '3D模型展示',\n                '语音聊天',\n                '屏幕共享',\n                'AI助手',\n                '实时协作'\n            ],\n            location: '虚拟建筑学院',\n            startTime: '2025-01-15T19:00:00Z'\n        },\n        {\n            id: '2',\n            title: 'AI创作者元宇宙派对',\n            description: 'AI艺术家和创作者的专属聚会空间，展示最新AI生成艺术作品，交流创作技巧和商业合作。',\n            host: {\n                name: 'AI艺术家·小创',\n                avatar: '🎨',\n                verified: true,\n                followers: 28900,\n                reputation: 88\n            },\n            stats: {\n                participants: 567,\n                likes: 3450,\n                comments: 890,\n                shares: 234\n            },\n            tags: [\n                'AI艺术',\n                '创作者经济',\n                'NFT展示',\n                '商业合作',\n                '技术交流'\n            ],\n            media: {\n                type: 'metaverse-event',\n                url: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=800&fit=crop',\n                modelUrl: '/models/creator-party.glb'\n            },\n            spaceType: 'event',\n            capacity: 1000,\n            currentUsers: 567,\n            features: [\n                'NFT画廊',\n                '音乐DJ',\n                '互动游戏',\n                '商务洽谈',\n                'AI生成艺术'\n            ],\n            location: '创作者元宇宙中心',\n            startTime: '2025-01-15T20:00:00Z'\n        },\n        {\n            id: '3',\n            title: '工程师技术分享会',\n            description: '全球工程师的技术分享和学习空间，讨论最新技术趋势、开源项目和职业发展。',\n            host: {\n                name: '技术专家·李工程师',\n                avatar: '⚙️',\n                verified: true,\n                followers: 45200,\n                reputation: 92\n            },\n            stats: {\n                participants: 890,\n                likes: 5670,\n                comments: 1234,\n                shares: 456\n            },\n            tags: [\n                '技术分享',\n                '开源项目',\n                '职业发展',\n                '编程技术',\n                '工程师社区'\n            ],\n            media: {\n                type: 'virtual-room',\n                url: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',\n                thumbnail: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=800&fit=crop',\n                modelUrl: '/models/tech-meetup.glb'\n            },\n            spaceType: 'public',\n            capacity: 2000,\n            currentUsers: 890,\n            features: [\n                '代码演示',\n                '技术讲座',\n                '项目展示',\n                '招聘信息',\n                '导师指导'\n            ],\n            location: '全球技术中心',\n            startTime: '2025-01-15T21:00:00Z'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocialPage.useEffect\": ()=>{\n            setLoading(true);\n            setTimeout({\n                \"SocialPage.useEffect\": ()=>{\n                    setSpaces(mockSpaces);\n                    setLoading(false);\n                }\n            }[\"SocialPage.useEffect\"], 1000);\n        }\n    }[\"SocialPage.useEffect\"], []);\n    // 显示成功提示\n    const showToast = (message)=>{\n        setToastMessage(message);\n        setShowSuccessToast(true);\n        setTimeout(()=>setShowSuccessToast(false), 3000);\n    };\n    // 进入虚拟空间\n    const handleEnterSpace = (space)=>{\n        setSelectedSpace(space);\n        setShowEnterSpace(true);\n    };\n    const confirmEnterSpace = ()=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowEnterSpace(false);\n            showToast(\"成功进入 \".concat(selectedSpace === null || selectedSpace === void 0 ? void 0 : selectedSpace.title));\n        }, 2000);\n    };\n    // 发送消息\n    const handleSendMessage = ()=>{\n        if (!newMessage.trim()) return;\n        setSendingMessage(true);\n        setTimeout(()=>{\n            setSendingMessage(false);\n            showToast('消息发送成功');\n            setNewMessage('');\n        }, 1000);\n    };\n    // 文件上传\n    const handleFileUpload = ()=>{\n        setShowFileUpload(true);\n    };\n    const confirmFileUpload = (files)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowFileUpload(false);\n            showToast(\"成功上传 \".concat(files.length, \" 个文件\"));\n        }, 2000);\n    };\n    // 表情选择\n    const handleEmojiSelect = (emoji)=>{\n        setNewMessage((prev)=>prev + emoji);\n        setShowEmojiPicker(false);\n    };\n    // 群组操作\n    const handleJoinGroup = (groupId)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            showToast('成功加入群组');\n        }, 1500);\n    };\n    const handleLeaveGroup = (groupId)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            showToast('已退出群组');\n        }, 1500);\n    };\n    const handleEnterGroup = (groupId)=>{\n        showToast('正在进入群组聊天...');\n    };\n    const handleGroupSettings = (groupId)=>{\n        showToast('群组设置功能开发中...');\n    };\n    const handleCreateGroup = ()=>{\n        setShowCreateGroup(true);\n    };\n    const confirmCreateGroup = (groupData)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowCreateGroup(false);\n            showToast('群组创建成功');\n        }, 2000);\n    };\n    // NFT操作\n    const handleBuyNFT = (nft)=>{\n        setSelectedNFT(nft);\n        setShowBuyNFT(true);\n    };\n    const confirmBuyNFT = ()=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowBuyNFT(false);\n            showToast(\"成功购买 \".concat(selectedNFT === null || selectedNFT === void 0 ? void 0 : selectedNFT.title));\n        }, 2000);\n    };\n    const handleCreateNFT = ()=>{\n        setShowCreateNFT(true);\n    };\n    const confirmCreateNFT = (nftData)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowCreateNFT(false);\n            showToast('NFT创建成功');\n        }, 3000);\n    };\n    const handleNFTCategory = (category)=>{\n        showToast(\"正在浏览 \".concat(category, \" 分类...\"));\n    };\n    // DAO治理操作\n    const handleVote = (proposalId, vote)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            showToast(\"投票成功：\".concat(vote === 'for' ? '赞成' : '反对'));\n        }, 1500);\n    };\n    const handleCreateProposal = ()=>{\n        setShowCreateProposal(true);\n    };\n    const confirmCreateProposal = (proposalData)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowCreateProposal(false);\n            showToast('提案创建成功');\n        }, 2000);\n    };\n    const handleDelegateVote = ()=>{\n        setShowDelegateVote(true);\n    };\n    const confirmDelegateVote = (delegateData)=>{\n        setIsLoading(true);\n        setTimeout(()=>{\n            setIsLoading(false);\n            setShowDelegateVote(false);\n            showToast('投票权委托成功');\n        }, 1500);\n    };\n    const handleCommitteeDetail = (committee)=>{\n        setSelectedCommittee(committee);\n        setShowCommitteeDetail(true);\n    };\n    const formatNumber = (num)=>{\n        if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';\n        if (num >= 1000) return (num / 1000).toFixed(1) + 'k';\n        return num.toString();\n    };\n    const getSpaceTypeColor = (type)=>{\n        switch(type){\n            case 'public':\n                return 'bg-green-500';\n            case 'private':\n                return 'bg-red-500';\n            case 'premium':\n                return 'bg-yellow-500';\n            case 'event':\n                return 'bg-purple-500';\n            default:\n                return 'bg-gray-500';\n        }\n    };\n    const getSpaceTypeText = (type)=>{\n        switch(type){\n            case 'public':\n                return '公开';\n            case 'private':\n                return '私密';\n            case 'premium':\n                return '高级';\n            case 'event':\n                return '活动';\n            default:\n                return '未知';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-green-400 to-teal-500 rounded-2xl flex items-center justify-center mb-4 mx-auto animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white font-bold text-2xl\",\n                            children: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-lg mb-2\",\n                        children: \"社交元宇宙\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-sm\",\n                        children: \"连接虚拟空间中...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                lineNumber: 525,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n            lineNumber: 524,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-10 flex flex-col h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-black/20 backdrop-blur-lg border-b border-white/10 p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-green-400 to-teal-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-sm\",\n                                                children: \"\\uD83D\\uDE80\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: \"社交元宇宙\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-300\",\n                                                    children: \"虚拟空间 • 实时聊天 • 社交网络\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-white/20 rounded-full px-2 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs\",\n                                                children: \"1.2k 在线\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1 bg-white/10 rounded-lg p-1 overflow-x-auto\",\n                            children: [\n                                {\n                                    key: 'spaces',\n                                    label: '虚拟空间',\n                                    icon: '🌌'\n                                },\n                                {\n                                    key: 'chat',\n                                    label: '实时聊天',\n                                    icon: '💬'\n                                },\n                                {\n                                    key: 'community',\n                                    label: '社区群组',\n                                    icon: '👥'\n                                },\n                                {\n                                    key: 'friends',\n                                    label: '好友动态',\n                                    icon: '🤝'\n                                },\n                                {\n                                    key: 'nft',\n                                    label: 'NFT市场',\n                                    icon: '🎨'\n                                },\n                                {\n                                    key: 'dao',\n                                    label: 'DAO治理',\n                                    icon: '🏛️'\n                                }\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.key),\n                                    className: \"flex-shrink-0 flex items-center justify-center space-x-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === tab.key ? 'bg-green-500 text-white' : 'text-white/70 hover:text-white'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tab.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"whitespace-nowrap\",\n                                            children: tab.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, tab.key, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 541,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden\",\n                    children: [\n                        activeTab === 'spaces' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: spaces.map((space, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                children: space.host.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: space.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-green-300\",\n                                                                        children: space.host.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 text-xs text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            space.currentUsers,\n                                                                            \"/\",\n                                                                            space.capacity\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 \".concat(getSpaceTypeColor(space.spaceType), \" rounded-full text-xs font-medium mt-1 inline-block\"),\n                                                                children: getSpaceTypeText(space.spaceType)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300 mb-3\",\n                                                children: space.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: space.features.slice(0, 2).map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-green-500/20 rounded text-xs\",\n                                                                children: feature\n                                                            }, idx, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleEnterSpace(space),\n                                                        className: \"px-4 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium hover:from-green-600 hover:to-teal-600 transition-colors\",\n                                                        children: \"\\uD83D\\uDE80 进入空间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 19\n                                    }, this)\n                                }, space.id, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 590,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-b border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 overflow-x-auto\",\n                                        children: chatRooms.map((room)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedChatRoom(room.id),\n                                                className: \"flex-shrink-0 flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors \".concat(selectedChatRoom === room.id ? 'bg-green-500 border-green-500 text-white' : 'bg-white/10 border-white/20 text-gray-300 hover:border-white/40'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: room.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: room.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-white/20 px-1 rounded\",\n                                                        children: room.members\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, room.id, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto p-4 space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl mb-2\",\n                                                    children: (_chatRooms_find = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find === void 0 ? void 0 : _chatRooms_find.icon\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (_chatRooms_find1 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find1 === void 0 ? void 0 : _chatRooms_find1.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: (_chatRooms_find2 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find2 === void 0 ? void 0 : _chatRooms_find2.description\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-green-400 mt-1\",\n                                                    children: [\n                                                        (_chatRooms_find3 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find3 === void 0 ? void 0 : _chatRooms_find3.members,\n                                                        \" 成员在线\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 17\n                                        }, this),\n                                        [\n                                            {\n                                                id: 1,\n                                                user: '🏗️ 建筑师小王',\n                                                message: '刚完成了一个AI辅助的建筑设计，效果很棒！',\n                                                time: '2分钟前',\n                                                isMe: false,\n                                                avatar: '🏗️'\n                                            },\n                                            {\n                                                id: 2,\n                                                user: '我',\n                                                message: '能分享一下设计图吗？很想看看AI的效果',\n                                                time: '1分钟前',\n                                                isMe: true,\n                                                avatar: '👨‍💻'\n                                            },\n                                            {\n                                                id: 3,\n                                                user: '🎨 AI艺术家',\n                                                message: '我也在用AI创作NFT，最近很火呢',\n                                                time: '30秒前',\n                                                isMe: false,\n                                                avatar: '🎨'\n                                            },\n                                            {\n                                                id: 4,\n                                                user: '🌐 Web3开发者',\n                                                message: '有人想合作开发DApp吗？',\n                                                time: '刚刚',\n                                                isMe: false,\n                                                avatar: '🌐'\n                                            }\n                                        ].map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex \".concat(msg.isMe ? 'justify-end' : 'justify-start'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-w-[80%] flex \".concat(msg.isMe ? 'flex-row-reverse' : 'flex-row', \" items-start space-x-2\"),\n                                                    children: [\n                                                        !msg.isMe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-sm\",\n                                                            children: msg.avatar\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\".concat(msg.isMe ? 'bg-green-500 mr-2' : 'bg-white/10', \" rounded-lg p-3\"),\n                                                            children: [\n                                                                !msg.isMe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-300 mb-1\",\n                                                                    children: msg.user\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 39\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-white\",\n                                                                    children: msg.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400 mt-1\",\n                                                                    children: msg.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, msg.id, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 pb-20 border-t border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-gray-400 hover:text-white\",\n                                                children: \"\\uD83D\\uDCCE\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newMessage,\n                                                onChange: (e)=>setNewMessage(e.target.value),\n                                                placeholder: \"在 \".concat((_chatRooms_find4 = chatRooms.find((r)=>r.id === selectedChatRoom)) === null || _chatRooms_find4 === void 0 ? void 0 : _chatRooms_find4.name, \" 中发消息...\"),\n                                                className: \"flex-1 bg-white/10 text-white px-4 py-3 rounded-full border border-white/20 focus:border-green-500 focus:outline-none\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-gray-400 hover:text-white\",\n                                                children: \"\\uD83D\\uDE0A\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl\",\n                                                    children: \"\\uD83D\\uDE80\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'community' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"⭐\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"推荐群组\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: communityGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                            children: group.icon\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 740,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-bold text-white\",\n                                                                                    children: group.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                    lineNumber: 744,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-green-300\",\n                                                                                    children: [\n                                                                                        group.members.toLocaleString(),\n                                                                                        \" 成员\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                    lineNumber: 745,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 743,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 rounded-full \".concat(group.activity === 'high' ? 'bg-green-400' : group.activity === 'medium' ? 'bg-yellow-400' : 'bg-gray-400')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: group.activity === 'high' ? '活跃' : group.activity === 'medium' ? '一般' : '较少'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-300 mb-3\",\n                                                            children: group.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                                            children: group.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-green-500/20 rounded text-xs text-green-300\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        tag\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 764,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400 capitalize\",\n                                                                    children: group.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 771,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(group.isJoined ? 'bg-gray-600 text-gray-300 hover:bg-gray-500' : 'bg-gradient-to-r from-green-500 to-teal-500 text-white hover:from-green-600 hover:to-teal-600'),\n                                                                    children: group.isJoined ? '已加入' : '加入群组'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, group.id, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDC65\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"我的群组\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: communityGroups.filter((g)=>g.isJoined).map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center\",\n                                                                        children: group.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 798,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-white\",\n                                                                                children: group.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: [\n                                                                                    group.members.toLocaleString(),\n                                                                                    \" 成员\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 803,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 801,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"px-3 py-1 bg-green-500 rounded-lg text-xs font-medium\",\n                                                                        children: \"进入\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium text-gray-400\",\n                                                                        children: \"设置\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 810,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, group.id, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 788,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"➕\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"创建群组\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300 mb-4\",\n                                            children: \"创建专属的专业群组，聚集志同道合的伙伴，共同探讨技术和创意。\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 826,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-white font-medium\",\n                                            children: \"\\uD83D\\uDE80 创建新群组\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 728,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'friends' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                {\n                                    id: 1,\n                                    user: '🏗️ 建筑师小王',\n                                    action: '发布了新的BIM模型',\n                                    content: 'AI驱动的智慧建筑设计',\n                                    time: '5分钟前',\n                                    likes: 23\n                                },\n                                {\n                                    id: 2,\n                                    user: '🎨 AI艺术家',\n                                    action: '创建了NFT作品',\n                                    content: '赛博朋克风格的未来城市',\n                                    time: '15分钟前',\n                                    likes: 45\n                                },\n                                {\n                                    id: 3,\n                                    user: '🚀 元宇宙设计师',\n                                    action: '加入了虚拟空间',\n                                    content: '建筑师专业交流会',\n                                    time: '30分钟前',\n                                    likes: 12\n                                }\n                            ].map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: activity.user.split(' ')[0]\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: activity.user.split(' ')[1]\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 856,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: [\n                                                                    \" \",\n                                                                    activity.action\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-300 mt-1\",\n                                                        children: activity.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 859,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 mt-2 text-xs text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: activity.time\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 861,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"flex items-center space-x-1 text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"❤️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 863,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: activity.likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 864,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 862,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"\\uD83D\\uDCAC 回复\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 866,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 854,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 19\n                                    }, this)\n                                }, activity.id, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'nft' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83C\\uDFA8\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"NFT市场\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-purple-400\",\n                                                            children: \"12,456\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 886,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: \"总NFT数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 887,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-green-400\",\n                                                            children: \"2,890\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 890,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-green-200\",\n                                                            children: \"活跃创作者\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 891,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-blue-400\",\n                                                            children: \"156.8 ETH\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 894,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-blue-200\",\n                                                            children: \"24h交易量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-yellow-400\",\n                                                            children: \"0.45 ETH\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-yellow-200\",\n                                                            children: \"平均价格\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 899,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 879,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"\\uD83D\\uDD25 热门NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: nftMarketplace.map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-gray-600 rounded-lg overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: nft.image,\n                                                                    alt: nft.title,\n                                                                    className: \"w-full h-full object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 912,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 p-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                className: \"font-medium text-white text-sm\",\n                                                                                children: nft.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 920,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    nft.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-blue-400\",\n                                                                                        children: \"✓\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                        lineNumber: 922,\n                                                                                        columnNumber: 48\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(nft.rarity === 'legendary' ? 'bg-yellow-500' : nft.rarity === 'epic' ? 'bg-purple-500' : nft.rarity === 'rare' ? 'bg-blue-500' : 'bg-gray-500'),\n                                                                                        children: nft.rarity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                        lineNumber: 923,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 921,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 919,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mb-2\",\n                                                                        children: nft.creator\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 932,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-green-400 font-bold text-sm\",\n                                                                                        children: nft.price\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                        lineNumber: 935,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-400\",\n                                                                                        children: [\n                                                                                            \"≈ $\",\n                                                                                            (parseFloat(nft.price) * 2500).toFixed(0)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                        lineNumber: 936,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 934,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-1 text-xs text-gray-400\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"❤️\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                                lineNumber: 940,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: nft.likes\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                                lineNumber: 941,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                        lineNumber: 939,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-xs font-medium\",\n                                                                                        children: \"购买\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                        lineNumber: 943,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                                lineNumber: 938,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 933,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                lineNumber: 918,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                        lineNumber: 910,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, nft.id, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 909,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 907,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"\\uD83D\\uDDBC️ 我的NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 957,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3\",\n                                            children: [\n                                                {\n                                                    id: 'my-1',\n                                                    title: '工程设计图',\n                                                    price: '0.2 ETH',\n                                                    status: 'owned'\n                                                },\n                                                {\n                                                    id: 'my-2',\n                                                    title: 'AI生成艺术',\n                                                    price: '0.15 ETH',\n                                                    status: 'selling'\n                                                }\n                                            ].map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"aspect-square bg-gray-600 rounded-lg mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"font-medium text-white text-sm mb-1\",\n                                                            children: nft.title\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 965,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 text-xs font-bold\",\n                                                                    children: nft.price\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 967,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(nft.status === 'owned' ? 'bg-blue-500' : 'bg-green-500'),\n                                                                    children: nft.status === 'owned' ? '持有' : '出售中'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 968,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 966,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, nft.id, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 963,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full mt-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-white font-medium\",\n                                            children: \"\\uD83C\\uDFA8 创建NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"\\uD83D\\uDCC2 NFT分类\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3\",\n                                            children: [\n                                                {\n                                                    name: '建筑设计',\n                                                    icon: '🏗️',\n                                                    count: 1234\n                                                },\n                                                {\n                                                    name: 'AI艺术',\n                                                    icon: '🤖',\n                                                    count: 2890\n                                                },\n                                                {\n                                                    name: '概念设计',\n                                                    icon: '💡',\n                                                    count: 567\n                                                },\n                                                {\n                                                    name: '3D模型',\n                                                    icon: '🎯',\n                                                    count: 890\n                                                }\n                                            ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-white/5 rounded-lg p-3 text-left hover:bg-white/10 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: category.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 994,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-white text-sm\",\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 995,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 993,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                category.count,\n                                                                \" 个NFT\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 997,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, category.name, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 992,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 877,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === 'dao' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83C\\uDFDB️\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1011,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"NextGen DAO\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1012,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-400\",\n                                                            children: \"25,680\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1016,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-green-200\",\n                                                            children: \"NGT持有者\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1017,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1015,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-400\",\n                                                            children: \"156\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-blue-200\",\n                                                            children: \"活跃提案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1021,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1019,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-400\",\n                                                            children: \"89%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1024,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: \"参与率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1025,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1023,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-yellow-400\",\n                                                            children: \"4.8M\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1028,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-yellow-200\",\n                                                            children: \"总投票权\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1029,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1027,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1014,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/5 rounded-lg p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300\",\n                                                children: \"NextGen DAO是一个去中心化自治组织，由社区成员共同治理平台的发展方向。 持有NGT代币即可参与提案投票，共同决定平台的未来。\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1009,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"活跃提案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1042,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                {\n                                                    id: 'prop-042',\n                                                    title: '降低平台交易手续费至2%',\n                                                    description: '建议将NFT交易手续费从3%降低至2%，以提高平台竞争力',\n                                                    status: 'voting',\n                                                    votes: {\n                                                        for: 15420,\n                                                        against: 3280\n                                                    },\n                                                    endTime: '2025-01-20',\n                                                    category: 'economic'\n                                                },\n                                                {\n                                                    id: 'prop-043',\n                                                    title: '新增VR虚拟展厅功能',\n                                                    description: '为创作者提供VR虚拟展厅，展示NFT作品集',\n                                                    status: 'voting',\n                                                    votes: {\n                                                        for: 12890,\n                                                        against: 1560\n                                                    },\n                                                    endTime: '2025-01-22',\n                                                    category: 'feature'\n                                                },\n                                                {\n                                                    id: 'prop-044',\n                                                    title: '建立创作者扶持基金',\n                                                    description: '从平台收益中拨出10%建立创作者扶持基金',\n                                                    status: 'passed',\n                                                    votes: {\n                                                        for: 18920,\n                                                        against: 2340\n                                                    },\n                                                    endTime: '2025-01-15',\n                                                    category: 'community'\n                                                }\n                                            ].map((proposal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: proposal.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 1075,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(proposal.status === 'voting' ? 'bg-yellow-500' : proposal.status === 'passed' ? 'bg-green-500' : 'bg-red-500'),\n                                                                    children: proposal.status === 'voting' ? '投票中' : proposal.status === 'passed' ? '已通过' : '未通过'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 1076,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1074,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-300 mb-3\",\n                                                            children: proposal.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1085,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-xs mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-400\",\n                                                                            children: [\n                                                                                \"赞成: \",\n                                                                                proposal.votes.for.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 1090,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: [\n                                                                                \"反对: \",\n                                                                                proposal.votes.against.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 1091,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 1089,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-white/20 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full\",\n                                                                        style: {\n                                                                            width: \"\".concat(proposal.votes.for / (proposal.votes.for + proposal.votes.against) * 100, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                        lineNumber: 1094,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 1093,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: proposal.status === 'voting' ? \"截止: \".concat(proposal.endTime) : \"结束: \".concat(proposal.endTime)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 1104,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                proposal.status === 'voting' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"px-3 py-1 bg-green-500 rounded-lg text-xs font-medium\",\n                                                                            children: \"赞成\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 1109,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"px-3 py-1 bg-red-500 rounded-lg text-xs font-medium\",\n                                                                            children: \"反对\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                            lineNumber: 1112,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 1108,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1103,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, proposal.id, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1073,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1043,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1041,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"专业委员会\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1125,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3\",\n                                            children: [\n                                                {\n                                                    name: '技术委员会',\n                                                    icon: '⚙️',\n                                                    members: 12,\n                                                    description: '技术路线制定'\n                                                },\n                                                {\n                                                    name: '内容委员会',\n                                                    icon: '📝',\n                                                    members: 8,\n                                                    description: '内容质量监管'\n                                                },\n                                                {\n                                                    name: '经济委员会',\n                                                    icon: '💰',\n                                                    members: 10,\n                                                    description: '代币经济设计'\n                                                },\n                                                {\n                                                    name: '仲裁委员会',\n                                                    icon: '⚖️',\n                                                    members: 6,\n                                                    description: '争议处理'\n                                                }\n                                            ].map((committee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 rounded-lg p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: committee.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 1135,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-white text-sm\",\n                                                                    children: committee.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 1136,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1134,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mb-2\",\n                                                            children: committee.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1138,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-400\",\n                                                                    children: [\n                                                                        committee.members,\n                                                                        \" 成员\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 1140,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-xs text-blue-400 hover:text-blue-300\",\n                                                                    children: \"查看详情\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                                    lineNumber: 1141,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1139,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, committee.name, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1133,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1126,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1124,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"我的治理参与\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1150,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-purple-400\",\n                                                            children: \"1,250\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-purple-200\",\n                                                            children: \"投票权重\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-green-400\",\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1157,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-green-200\",\n                                                            children: \"参与提案\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1158,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1156,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-yellow-400\",\n                                                            children: \"89%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-yellow-200\",\n                                                            children: \"参与率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                            lineNumber: 1162,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1160,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1151,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg text-sm font-medium\",\n                                                    children: \"创建提案\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex-1 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-sm font-medium\",\n                                                    children: \"委托投票\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                                    lineNumber: 1169,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                            lineNumber: 1165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                            lineNumber: 1007,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n            lineNumber: 539,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\social\\\\page.tsx\",\n        lineNumber: 537,\n        columnNumber: 5\n    }, this);\n}\n_s(SocialPage, \"kRtG0ufnbvKbSTX1o4r7uCim4y8=\");\n_c = SocialPage;\nvar _c;\n$RefreshReg$(_c, \"SocialPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/social/page.tsx\n"));

/***/ })

});