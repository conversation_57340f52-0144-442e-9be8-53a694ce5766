"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/creator-economy-homepage.tsx":
/*!**********************************************!*\
  !*** ./src/app/creator-economy-homepage.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreatorEconomyHomepage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_PointsManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/PointsManager */ \"(app-pages-browser)/./src/components/PointsManager.tsx\");\n/* harmony import */ var _components_DigitalAssetMarket__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DigitalAssetMarket */ \"(app-pages-browser)/./src/components/DigitalAssetMarket.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 创作者经济引擎首页组件\nfunction CreatorEconomyHomepage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // 状态管理\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [userVerified, setUserVerified] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('user-001');\n    const [showPointsModal, setShowPointsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAssetModal, setShowAssetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTipModal, setShowTipModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMenuModal, setShowMenuModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSearchModal, setShowSearchModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('recommend');\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showFollowModal, setShowFollowModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCommentModal, setShowCommentModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSaved, setIsSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFollowing, setIsFollowing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newComment, setNewComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const commentInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [showCreatorModal, setShowCreatorModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEconomyModal, setShowEconomyModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLevelModal, setShowLevelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userPoints, setUserPoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        NGT: 1000,\n        CRT: 500,\n        SKL: 300,\n        FAN: 800,\n        DID: 95\n    });\n    const [barrageMessages, setBarrageMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            text: '这个设计太棒了！',\n            user: '建筑师小王',\n            time: Date.now()\n        },\n        {\n            id: '2',\n            text: 'AI技术真的改变了建筑行业',\n            user: '设计爱好者',\n            time: Date.now() - 1000\n        },\n        {\n            id: '3',\n            text: '想学习这个技术',\n            user: '学生小李',\n            time: Date.now() - 2000\n        }\n    ]);\n    // 创作者经济内容数据\n    const contents = [\n        {\n            id: 'content-1',\n            title: 'AI驱动的智慧城市建设',\n            description: '🏗️ 使用AI驱动的建筑设计，创造未来智慧城市综合体。集成VR漫游、智能管理和IoT控制系统。',\n            creator: {\n                name: 'AI建筑大师',\n                avatar: '🏗️',\n                verified: true,\n                level: 'Legend',\n                followers: 1250000,\n                revenue: 156800\n            },\n            media: {\n                url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=800&fit=crop',\n                type: 'video'\n            },\n            engagement: {\n                views: 2340000,\n                likes: 189500,\n                comments: 24800,\n                shares: 18900,\n                saves: 45600\n            },\n            digitalAsset: {\n                isDigitalAsset: true,\n                price: 5000,\n                currency: 'CNY',\n                category: '设计方案',\n                copyrightProtected: true\n            },\n            economy: {\n                totalRevenue: 156800,\n                monthlyIncome: 52000,\n                fanCount: 1250000,\n                tier: 'Platinum'\n            },\n            ai: {\n                score: 9.2,\n                reason: '基于您对建筑设计的兴趣推荐',\n                trending: true\n            },\n            tags: [\n                'AI建筑',\n                '智慧城市',\n                '未来设计'\n            ]\n        },\n        {\n            id: 'content-2',\n            title: '元宇宙社交空间设计',\n            description: '🚀 基于WebXR的沉浸式社交元宇宙，支持虚拟身份、空间音频、AI社交助手。',\n            creator: {\n                name: 'VR创作者',\n                avatar: '🚀',\n                verified: true,\n                level: 'Elite',\n                followers: 890000,\n                revenue: 89600\n            },\n            media: {\n                url: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=800&fit=crop',\n                type: 'video'\n            },\n            engagement: {\n                views: 1560000,\n                likes: 125000,\n                comments: 18900,\n                shares: 12400,\n                saves: 28900\n            },\n            digitalAsset: {\n                isDigitalAsset: true,\n                price: 3000,\n                currency: 'CNY',\n                category: '3D模型',\n                copyrightProtected: true\n            },\n            economy: {\n                totalRevenue: 89600,\n                monthlyIncome: 28000,\n                fanCount: 890000,\n                tier: 'Gold'\n            },\n            ai: {\n                score: 8.8,\n                reason: '基于您对元宇宙技术的关注推荐',\n                trending: true\n            },\n            tags: [\n                '元宇宙',\n                'WebXR',\n                'AI社交'\n            ]\n        }\n    ];\n    // 关注内容数据\n    const followContents = [\n        {\n            ...contents[0],\n            creator: {\n                ...contents[0].creator,\n                name: '关注的建筑师',\n                followers: 50000\n            }\n        }\n    ];\n    // 附近内容数据\n    const nearbyContents = [\n        {\n            ...contents[1],\n            creator: {\n                ...contents[1].creator,\n                name: '附近的设计师',\n                followers: 25000\n            },\n            location: '距离您 2.5km'\n        }\n    ];\n    // 根据当前标签页获取内容\n    const getCurrentContents = ()=>{\n        switch(activeTab){\n            case 'follow':\n                return followContents;\n            case 'nearby':\n                return nearbyContents;\n            default:\n                return contents;\n        }\n    };\n    const currentContents = getCurrentContents();\n    const currentContent = currentContents[currentIndex] || contents[0];\n    // 实名认证\n    const handleVerification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CreatorEconomyHomepage.useCallback[handleVerification]\": async ()=>{\n            try {\n                // 模拟实名认证流程\n                const confirmed = confirm('是否进行实名认证？\\n实名认证后可享受更多平台服务');\n                if (confirmed) {\n                    // 这里应该跳转到实名认证页面\n                    alert('正在跳转到实名认证页面...');\n                    setUserVerified(true);\n                }\n            } catch (error) {\n                console.error('实名认证失败:', error);\n                alert('实名认证失败，请稍后重试');\n            }\n        }\n    }[\"CreatorEconomyHomepage.useCallback[handleVerification]\"], []);\n    // 格式化数字\n    const formatNumber = (num)=>{\n        if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';\n        if (num >= 1000) return (num / 1000).toFixed(1) + 'k';\n        return num.toString();\n    };\n    // 格式化价格\n    const formatPrice = (price)=>{\n        return \"\\xa5\".concat(price.toLocaleString());\n    };\n    // 处理积分更新\n    const handlePointsUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CreatorEconomyHomepage.useCallback[handlePointsUpdate]\": (newPoints)=>{\n            setUserPoints(newPoints);\n        }\n    }[\"CreatorEconomyHomepage.useCallback[handlePointsUpdate]\"], []);\n    // 处理数字资产购买\n    const handleAssetPurchase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CreatorEconomyHomepage.useCallback[handleAssetPurchase]\": (asset)=>{\n            console.log('购买数字资产:', asset);\n        // 这里可以添加购买成功后的处理逻辑\n        }\n    }[\"CreatorEconomyHomepage.useCallback[handleAssetPurchase]\"], []);\n    // 处理交互\n    const handleLike = ()=>{\n        setIsLiked(!isLiked);\n        // 模拟点赞数变化\n        console.log(isLiked ? '取消点赞' : '点赞成功');\n    };\n    const handleComment = ()=>{\n        setShowCommentModal(true);\n        // 延迟聚焦，等待弹窗动画完成\n        setTimeout(()=>{\n            var _commentInputRef_current;\n            (_commentInputRef_current = commentInputRef.current) === null || _commentInputRef_current === void 0 ? void 0 : _commentInputRef_current.focus();\n        }, 300);\n    };\n    const handleShare = ()=>setShowShareModal(true);\n    const handleSave = ()=>{\n        setIsSaved(!isSaved);\n        console.log(isSaved ? '取消收藏' : '收藏成功');\n    };\n    const handleFollow = ()=>{\n        setIsFollowing(!isFollowing);\n        console.log(isFollowing ? '取消关注' : '关注成功');\n    };\n    const handleNFTPurchase = ()=>setShowAssetModal(true);\n    const handleTip = ()=>setShowTipModal(true);\n    // 滑动切换\n    const handleSwipe = (direction)=>{\n        const maxIndex = currentContents.length - 1;\n        if (direction === 'up' && currentIndex < maxIndex) {\n            setCurrentIndex(currentIndex + 1);\n        } else if (direction === 'down' && currentIndex > 0) {\n            setCurrentIndex(currentIndex - 1);\n        }\n    };\n    // 标签页切换时重置索引\n    const handleTabChange = (tab)=>{\n        setActiveTab(tab);\n        setCurrentIndex(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black text-white overflow-hidden relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: currentContent.media.url,\n                        alt: currentContent.title,\n                        fill: true,\n                        className: \"object-cover\",\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/40\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 right-0 z-20 pt-12 pb-4 px-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 py-1 bg-red-500/80 rounded-full flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xs font-medium\",\n                                                    children: \"LIVE\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        userVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 py-1 bg-green-500/80 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xs\",\n                                                children: \"✓ 已认证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleTabChange('follow'),\n                                            className: \"text-sm transition-colors \".concat(activeTab === 'follow' ? 'text-white font-bold border-b-2 border-white pb-1' : 'text-white/60 hover:text-white/80'),\n                                            children: \"关注\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleTabChange('recommend'),\n                                                    className: \"text-sm transition-colors \".concat(activeTab === 'recommend' ? 'text-white font-bold border-b-2 border-white pb-1' : 'text-white/60 hover:text-white/80'),\n                                                    children: \"推荐\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === 'recommend' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleTabChange('nearby'),\n                                            className: \"text-sm transition-colors \".concat(activeTab === 'nearby' ? 'text-white font-bold border-b-2 border-white pb-1' : 'text-white/60 hover:text-white/80'),\n                                            children: \"附近\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSearchModal(true),\n                                            className: \"w-8 h-8 flex items-center justify-center hover:bg-white/10 rounded-full transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowMenuModal(true),\n                                            className: \"w-8 h-8 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 text-white\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/3 left-4 right-20 z-15\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                            children: barrageMessages.slice(-3).map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 100\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        x: -100\n                                    },\n                                    transition: {\n                                        delay: index * 0.5\n                                    },\n                                    className: \"mb-2 bg-black/40 backdrop-blur-sm rounded-full px-3 py-1 max-w-64\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-cyan-400\",\n                                            children: [\n                                                msg.user,\n                                                \": \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-white\",\n                                            children: msg.text\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, msg.id, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 right-0 z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-end justify-between p-4 pb-24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 pr-4 max-w-[70%]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowCreatorModal(true),\n                                                    className: \"flex items-center space-x-2 hover:bg-white/10 rounded-lg px-2 py-1 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-semibold text-base\",\n                                                            children: [\n                                                                \"@\",\n                                                                currentContent.creator.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        currentContent.creator.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-blue-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowLevelModal(true),\n                                                    className: \"px-2 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded text-xs font-bold text-black hover:scale-105 transition-transform\",\n                                                    children: currentContent.creator.level\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(activeTab === 'follow' ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30' : activeTab === 'nearby' ? 'bg-green-500/20 text-green-400 border border-green-500/30' : 'bg-purple-500/20 text-purple-400 border border-purple-500/30'),\n                                                    children: activeTab === 'follow' ? '👥 关注' : activeTab === 'nearby' ? '📍 附近' : '🔥 推荐'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this),\n                                                currentContent.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-2 py-1 bg-green-500/20 text-green-400 border border-green-500/30 rounded-full text-xs\",\n                                                    children: [\n                                                        \"\\uD83D\\uDCCD \",\n                                                        currentContent.location\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowEconomyModal(true),\n                                                    className: \"flex items-center space-x-1 hover:bg-green-500/10 rounded-lg px-2 py-1 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-green-400\",\n                                                            children: \"\\uD83D\\uDCB0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-white\",\n                                                            children: [\n                                                                formatNumber(currentContent.economy.monthlyIncome),\n                                                                \"/月\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowCreatorModal(true),\n                                                    className: \"flex items-center space-x-1 hover:bg-purple-500/10 rounded-lg px-2 py-1 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-purple-400\",\n                                                            children: \"\\uD83D\\uDC65\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-white\",\n                                                            children: formatNumber(currentContent.creator.followers)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowEconomyModal(true),\n                                                    className: \"px-2 py-1 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded border border-purple-500/30 hover:bg-purple-500/30 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-purple-300\",\n                                                        children: currentContent.economy.tier\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-sm leading-relaxed line-clamp-3\",\n                                                children: currentContent.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                            children: currentContent.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: [\n                                                        \"#\",\n                                                        tag\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleFollow,\n                                                        className: \"w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-lg border-2 border-white hover:scale-105 transition-transform\",\n                                                        children: currentContent.creator.avatar\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-xs\",\n                                                            children: currentContent.creator.level === 'Legend' ? '👑' : currentContent.creator.level === 'Elite' ? '⭐' : currentContent.creator.level === 'Popular' ? '🔥' : '🌟'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                        onClick: handleFollow,\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        className: \"absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full flex items-center justify-center border-2 border-white transition-colors \".concat(isFollowing ? 'bg-green-500 text-white' : 'bg-red-500 text-white'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-bold\",\n                                                            children: isFollowing ? '✓' : '+'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                            onClick: handleLike,\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\",\n                                                    children: \"\\uD83D\\uDC4D\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs mt-1 transition-colors \".concat(isLiked ? 'text-red-400' : 'text-white'),\n                                                    children: formatNumber(currentContent.engagement.likes + (isLiked ? 1 : 0))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleShare,\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\",\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs mt-1 text-white\",\n                                                    children: formatNumber(currentContent.engagement.shares)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                            onClick: handleSave,\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\",\n                                                    children: \"❤️\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs mt-1 transition-colors \".concat(isSaved ? 'text-yellow-400' : 'text-white'),\n                                                    children: formatNumber(currentContent.engagement.saves + (isSaved ? 1 : 0))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleComment,\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\",\n                                                    children: \"\\uD83D\\uDCAC\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs mt-1 text-white\",\n                                                    children: formatNumber(currentContent.engagement.comments)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, this),\n                                        currentContent.digitalAsset.isDigitalAsset && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAssetModal(true),\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"\\uD83D\\uDC8E\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs mt-1\",\n                                                    children: \"资产\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowPointsModal(true),\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"\\uD83C\\uDF1F\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs mt-1\",\n                                                    children: \"积分\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                            onClick: handleSave,\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center transition-colors \".concat(isSaved ? 'bg-yellow-500/30' : 'bg-white/20'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 transition-colors \".concat(isSaved ? 'text-yellow-400' : 'text-white'),\n                                                        fill: isSaved ? 'currentColor' : 'none',\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs mt-1 transition-colors \".concat(isSaved ? 'text-yellow-400' : 'text-white'),\n                                                    children: formatNumber(currentContent.engagement.saves + (isSaved ? 1 : 0))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-10\",\n                        onTouchStart: (e)=>{\n                            const touch = e.touches[0];\n                            const startY = touch.clientY;\n                            const handleTouchMove = (e)=>{\n                                const touch = e.touches[0];\n                                const deltaY = touch.clientY - startY;\n                                if (Math.abs(deltaY) > 50) {\n                                    if (deltaY > 0) {\n                                        handleSwipe('down');\n                                    } else {\n                                        handleSwipe('up');\n                                    }\n                                    document.removeEventListener('touchmove', handleTouchMove);\n                                }\n                            };\n                            document.addEventListener('touchmove', handleTouchMove);\n                            document.addEventListener('touchend', ()=>{\n                                document.removeEventListener('touchmove', handleTouchMove);\n                            }, {\n                                once: true\n                            });\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 595,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showPointsModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                    onClick: ()=>setShowPointsModal(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl max-w-md w-full border border-blue-500/30 max-h-[80vh] overflow-y-auto\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-4\",\n                                            children: \"\\uD83C\\uDF1F\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-2\",\n                                            children: \"积分管理\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"管理您的平台积分\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PointsManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    userId: userId,\n                                    onPointsUpdate: handlePointsUpdate\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPointsModal(false),\n                                        className: \"w-full py-3 bg-gray-700 rounded-xl text-white font-medium\",\n                                        children: \"关闭\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 624,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showAssetModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                    onClick: ()=>setShowAssetModal(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl max-w-md w-full border border-green-500/30 max-h-[80vh] overflow-y-auto\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-4\",\n                                            children: \"\\uD83D\\uDC8E\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-2\",\n                                            children: \"数字资产市场\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"浏览和购买数字资产\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DigitalAssetMarket__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    onPurchase: handleAssetPurchase\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAssetModal(false),\n                                        className: \"w-full py-3 bg-gray-700 rounded-xl text-white font-medium\",\n                                        children: \"关闭\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 676,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                    lineNumber: 669,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 667,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showTipModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                    onClick: ()=>setShowTipModal(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-yellow-500/30\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDCB0\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white mb-2\",\n                                    children: \"积分打赏\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm mb-4\",\n                                    children: [\n                                        \"支持 @\",\n                                        currentContent.creator.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-3 mb-4\",\n                                    children: [\n                                        100,\n                                        500,\n                                        1000\n                                    ].map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"py-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-xl text-white font-medium hover:bg-yellow-500/30 transition-colors\",\n                                            children: [\n                                                amount,\n                                                \" FAN\"\n                                            ]\n                                        }, amount, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-200 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium mb-1\",\n                                                children: \"打赏说明：\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"• 使用FAN积分进行打赏\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"• 积分为平台内虚拟积分，不具有货币属性\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"• 打赏将增加创作者的粉丝积分\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowTipModal(false),\n                                            className: \"flex-1 py-3 bg-gray-700 rounded-xl text-white font-medium\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                console.log('发送积分打赏');\n                                                alert('积分打赏成功！感谢您对创作者的支持！');\n                                                setShowTipModal(false);\n                                            },\n                                            className: \"flex-1 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl text-black font-medium\",\n                                            children: \"确认打赏\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 748,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 716,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                    lineNumber: 709,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 707,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showMenuModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                    onClick: ()=>setShowMenuModal(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-4\",\n                                        children: \"⚙️\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 790,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-white mb-2\",\n                                        children: \"菜单\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"更多功能和设置\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowMenuModal(false);\n                                            router.push('/creator');\n                                        },\n                                        className: \"w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-pink-500/20 to-purple-500/20 border border-pink-500/30 rounded-xl hover:bg-pink-500/30 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl\",\n                                                children: \"✨\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"创作中心\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: \"发布内容和管理作品\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 797,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowMenuModal(false);\n                                            setShowTipModal(true);\n                                        },\n                                        className: \"w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-xl hover:bg-yellow-500/30 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDCB0\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"积分打赏\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: \"支持喜欢的创作者\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 820,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 812,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowMenuModal(false);\n                                            setShowAssetModal(true);\n                                        },\n                                        className: \"w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-xl hover:bg-blue-500/30 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDC8E\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 834,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"数字资产\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: \"浏览和购买数字作品\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 17\n                                    }, this),\n                                    !userVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowMenuModal(false);\n                                            router.push('/verification');\n                                        },\n                                        className: \"w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-xl hover:bg-green-500/30 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"实名认证\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: \"完成身份验证\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowMenuModal(false);\n                                            router.push('/legal');\n                                        },\n                                        className: \"w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-xl hover:bg-yellow-500/30 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl\",\n                                                children: \"⚖️\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 866,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"合规声明\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: \"法律条款和隐私政策\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 859,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowMenuModal(false);\n                                            router.push('/profile');\n                                        },\n                                        className: \"w-full flex items-center space-x-3 p-4 bg-white/10 border border-white/20 rounded-xl hover:bg-white/20 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl\",\n                                                children: \"⚙️\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"个人中心\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: \"账户设置和管理\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 795,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowMenuModal(false),\n                                    className: \"w-full py-3 bg-gray-700 rounded-xl text-white font-medium\",\n                                    children: \"关闭\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 889,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 782,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                    lineNumber: 775,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 773,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showSearchModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 bg-black/90 backdrop-blur-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center p-4 border-b border-white/10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSearchModal(false),\n                                        className: \"w-8 h-8 flex items-center justify-center mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-white\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 918,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 914,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                placeholder: \"搜索创作者、内容、技能...\",\n                                                className: \"w-full bg-white/10 border border-white/20 rounded-full px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500\",\n                                                autoFocus: true\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 913,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto\",\n                                children: searchQuery ? /* 搜索结果 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white font-bold mb-4\",\n                                            children: \"搜索结果\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 942,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-gray-400 text-sm mb-2\",\n                                                            children: \"创作者\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 946,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: contents.filter((c)=>c.creator.name.includes(searchQuery) || c.title.includes(searchQuery)).map((content, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3 p-3 bg-white/5 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: content.creator.avatar\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                                lineNumber: 951,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                            lineNumber: 950,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-white font-medium\",\n                                                                                    children: content.creator.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                                    lineNumber: 954,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-gray-400 text-sm\",\n                                                                                    children: [\n                                                                                        formatNumber(content.creator.followers),\n                                                                                        \" 关注者\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                                    lineNumber: 955,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                            lineNumber: 953,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"px-3 py-1 bg-blue-500 rounded-full text-white text-sm\",\n                                                                            children: \"关注\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                            lineNumber: 957,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 945,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-gray-400 text-sm mb-2\",\n                                                            children: \"相关内容\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 967,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-3\",\n                                                            children: contents.filter((c)=>c.tags.some((tag)=>tag.includes(searchQuery)) || c.description.includes(searchQuery)).map((content, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white/5 rounded-lg overflow-hidden\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"aspect-video bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-2xl\",\n                                                                                children: content.creator.avatar\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                                lineNumber: 972,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                            lineNumber: 971,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-white text-sm font-medium line-clamp-2\",\n                                                                                    children: content.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                                    lineNumber: 975,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-gray-400 text-xs mt-1\",\n                                                                                    children: [\n                                                                                        formatNumber(content.engagement.views),\n                                                                                        \" 观看\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                                    lineNumber: 976,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                            lineNumber: 974,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                    lineNumber: 970,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 966,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 19\n                                }, this) : /* 搜索建议 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white font-bold mb-4\",\n                                            children: \"热门搜索\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-6\",\n                                            children: [\n                                                'AI建筑',\n                                                '智慧城市',\n                                                'BIM设计',\n                                                '元宇宙',\n                                                '绿色建筑',\n                                                'VR漫游'\n                                            ].map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSearchQuery(tag),\n                                                    className: \"px-3 py-1 bg-white/10 rounded-full text-white text-sm hover:bg-white/20 transition-colors\",\n                                                    children: tag\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 990,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 988,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white font-bold mb-4\",\n                                            children: \"推荐创作者\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: contents.map((content, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 p-3 bg-white/5 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: content.creator.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 1004,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: content.creator.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                    lineNumber: 1008,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: [\n                                                                        formatNumber(content.creator.followers),\n                                                                        \" 关注者\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                    lineNumber: 1009,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"px-3 py-1 bg-blue-500 rounded-full text-white text-sm\",\n                                                            children: \"关注\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 1011,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1003,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 1001,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 986,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 911,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                    lineNumber: 905,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 903,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showCommentModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-[9999] bg-black/80 backdrop-blur-lg\",\n                    onClick: ()=>setShowCommentModal(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-end pb-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                y: '100%'\n                            },\n                            animate: {\n                                y: 0\n                            },\n                            exit: {\n                                y: '100%'\n                            },\n                            className: \"w-full bg-gradient-to-t from-gray-900 to-gray-800 rounded-t-3xl flex flex-col max-h-[75vh]\",\n                            onClick: (e)=>e.stopPropagation(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-6 pb-4 border-b border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: [\n                                                \"评论 \",\n                                                formatNumber(currentContent.engagement.comments)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 1046,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCommentModal(false),\n                                            className: \"w-8 h-8 bg-white/10 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1052,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1051,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 1047,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 1045,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            {\n                                                user: '建筑师小王',\n                                                avatar: '🏗️',\n                                                comment: '这个设计理念太前卫了！AI技术在建筑领域的应用真的让人惊叹',\n                                                time: '2分钟前',\n                                                likes: 12\n                                            },\n                                            {\n                                                user: '设计爱好者',\n                                                avatar: '🎨',\n                                                comment: '请问用的是什么软件建模的？效果太棒了',\n                                                time: '5分钟前',\n                                                likes: 8\n                                            },\n                                            {\n                                                user: '学生小李',\n                                                avatar: '📚',\n                                                comment: '作为建筑专业的学生，这给了我很多启发',\n                                                time: '10分钟前',\n                                                likes: 15\n                                            },\n                                            {\n                                                user: 'VR专家',\n                                                avatar: '🥽',\n                                                comment: 'VR漫游的体验一定很震撼，期待实际应用',\n                                                time: '15分钟前',\n                                                likes: 6\n                                            }\n                                        ].map((comment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: comment.avatar\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 1068,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/10 rounded-lg p-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-medium text-sm\",\n                                                                            children: comment.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                            lineNumber: 1073,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400 text-xs\",\n                                                                            children: comment.time\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                            lineNumber: 1074,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                    lineNumber: 1072,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm leading-relaxed\",\n                                                                    children: comment.comment\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                    lineNumber: 1076,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"flex items-center space-x-1 text-gray-400 hover:text-red-400 transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4\",\n                                                                                    fill: \"none\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                                        lineNumber: 1080,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                                    lineNumber: 1079,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: comment.likes\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                                    lineNumber: 1082,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                            lineNumber: 1078,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"text-gray-400 hover:text-blue-400 transition-colors text-xs\",\n                                                                            children: \"回复\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                            lineNumber: 1084,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                    lineNumber: 1077,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 1071,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1070,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1059,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 1058,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-auto border-t border-white/10 bg-gray-900 p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3 items-end\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: \"\\uD83D\\uDC64\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1099,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1098,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            ref: commentInputRef,\n                                                            type: \"text\",\n                                                            value: newComment,\n                                                            onChange: (e)=>setNewComment(e.target.value),\n                                                            placeholder: \"写下你的评论...\",\n                                                            className: \"flex-1 bg-white/10 border border-white/20 rounded-full px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:bg-white/15 transition-colors text-sm\",\n                                                            onKeyDown: (e)=>{\n                                                                if (e.key === 'Enter' && newComment.trim()) {\n                                                                    console.log('发送评论:', newComment);\n                                                                    setNewComment('');\n                                                                    alert('评论发送成功！');\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 1103,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (newComment.trim()) {\n                                                                    console.log('发送评论:', newComment);\n                                                                    setNewComment('');\n                                                                    alert('评论发送成功！');\n                                                                }\n                                                            },\n                                                            disabled: !newComment.trim(),\n                                                            className: \"px-4 py-3 rounded-full font-medium transition-colors flex-shrink-0 text-sm \".concat(newComment.trim() ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-gray-600 text-gray-400 cursor-not-allowed'),\n                                                            children: \"发送\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 1118,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1102,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1101,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1097,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 1096,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                            lineNumber: 1037,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 1036,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                    lineNumber: 1028,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 1026,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showShareModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                    onClick: ()=>setShowShareModal(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-4\",\n                                        children: \"\\uD83D\\uDCE4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1163,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-white mb-2\",\n                                        children: \"分享内容\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1164,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"分享这个精彩内容\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1165,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1162,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 mb-6\",\n                                children: [\n                                    {\n                                        name: '微信',\n                                        icon: '💚',\n                                        color: 'from-green-500 to-emerald-500'\n                                    },\n                                    {\n                                        name: '朋友圈',\n                                        icon: '🌟',\n                                        color: 'from-blue-500 to-cyan-500'\n                                    },\n                                    {\n                                        name: 'QQ',\n                                        icon: '🐧',\n                                        color: 'from-blue-600 to-blue-700'\n                                    },\n                                    {\n                                        name: '微博',\n                                        icon: '📱',\n                                        color: 'from-red-500 to-pink-500'\n                                    },\n                                    {\n                                        name: '复制链接',\n                                        icon: '🔗',\n                                        color: 'from-gray-500 to-gray-600'\n                                    },\n                                    {\n                                        name: '更多',\n                                        icon: '⋯',\n                                        color: 'from-purple-500 to-indigo-500'\n                                    }\n                                ].map((platform, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            console.log(\"分享到\".concat(platform.name));\n                                            alert(\"已分享到\".concat(platform.name));\n                                            setShowShareModal(false);\n                                        },\n                                        className: \"flex flex-col items-center p-4 bg-gradient-to-r \".concat(platform.color, \" rounded-xl hover:scale-105 transition-transform\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl mb-2\",\n                                                children: platform.icon\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1186,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xs font-medium\",\n                                                children: platform.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1187,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1177,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1168,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowShareModal(false),\n                                className: \"w-full py-3 bg-gray-700 rounded-xl text-white font-medium\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1192,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 1155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                    lineNumber: 1148,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 1146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showCreatorModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                    onClick: ()=>setShowCreatorModal(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-3xl mx-auto mb-4 border-4 border-white/20\",\n                                        children: currentContent.creator.avatar\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1222,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: [\n                                                    \"@\",\n                                                    currentContent.creator.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1226,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentContent.creator.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 text-blue-400\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1229,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1228,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1225,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full text-sm font-bold text-black\",\n                                                children: currentContent.creator.level\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1234,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-1 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full border border-purple-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-purple-300\",\n                                                    children: currentContent.economy.tier\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1238,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1237,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1233,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1221,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-3 bg-white/5 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: formatNumber(currentContent.creator.followers)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1246,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"关注者\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1247,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1245,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-3 bg-white/5 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-400\",\n                                                children: formatNumber(currentContent.economy.monthlyIncome)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1250,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"月收入\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1251,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1249,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center p-3 bg-white/5 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-purple-400\",\n                                                children: formatNumber(currentContent.engagement.views)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1254,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"总观看\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1255,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1253,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1244,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-3\",\n                                        children: \"创作者介绍\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1261,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm leading-relaxed mb-3\",\n                                                children: \"专注于AI驱动的建筑设计和智慧城市规划，拥有10年建筑行业经验。 擅长将前沿科技与传统建筑美学相结合，创造出既实用又美观的建筑作品。\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    'AI建筑',\n                                                    '智慧城市',\n                                                    'BIM设计',\n                                                    'VR漫游',\n                                                    '绿色建筑'\n                                                ].map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-blue-500/20 text-blue-400 rounded-full text-xs\",\n                                                        children: skill\n                                                    }, index, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1269,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1267,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1262,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1260,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-3\",\n                                        children: \"成就徽章\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1279,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-3\",\n                                        children: [\n                                            {\n                                                icon: '🏆',\n                                                name: '金牌创作者',\n                                                color: 'from-yellow-500 to-orange-500'\n                                            },\n                                            {\n                                                icon: '🔥',\n                                                name: '热门内容',\n                                                color: 'from-red-500 to-pink-500'\n                                            },\n                                            {\n                                                icon: '💎',\n                                                name: 'NFT先锋',\n                                                color: 'from-blue-500 to-purple-500'\n                                            },\n                                            {\n                                                icon: '🌟',\n                                                name: '粉丝之星',\n                                                color: 'from-purple-500 to-indigo-500'\n                                            }\n                                        ].map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-3 bg-gradient-to-r \".concat(badge.color, \" rounded-lg\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: badge.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1288,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-white font-medium\",\n                                                        children: badge.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1289,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1280,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1278,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            handleFollow();\n                                            setShowCreatorModal(false);\n                                        },\n                                        className: \"flex-1 py-3 rounded-xl font-medium transition-colors \".concat(isFollowing ? 'bg-gray-600 text-white' : 'bg-blue-500 text-white hover:bg-blue-600'),\n                                        children: isFollowing ? '已关注' : '关注'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowCreatorModal(false);\n                                            setShowTipModal(true);\n                                        },\n                                        className: \"flex-1 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl text-black font-medium hover:scale-105 transition-transform\",\n                                        children: \"打赏\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1310,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1296,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCreatorModal(false),\n                                className: \"w-full py-3 bg-gray-700 rounded-xl text-white font-medium\",\n                                children: \"关闭\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1321,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 1213,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                    lineNumber: 1206,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 1204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showEconomyModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                    onClick: ()=>setShowEconomyModal(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-green-500/30 max-h-[80vh] overflow-y-auto\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-4\",\n                                        children: \"\\uD83D\\uDCB0\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1350,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-white mb-2\",\n                                        children: \"创作者经济数据\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1351,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: [\n                                            \"@\",\n                                            currentContent.creator.name,\n                                            \" 的收益统计\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1352,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1349,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-400 mb-1\",\n                                                children: [\n                                                    \"\\xa5\",\n                                                    formatNumber(currentContent.economy.monthlyIncome)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1358,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"月收入\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1361,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1357,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/30 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-400 mb-1\",\n                                                children: [\n                                                    \"\\xa5\",\n                                                    formatNumber(currentContent.economy.totalRevenue)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1364,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"总收入\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1367,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1363,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1356,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-3\",\n                                        children: \"收入来源\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1373,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            {\n                                                source: '数字资产销售',\n                                                amount: 28000,\n                                                percentage: 54,\n                                                color: 'bg-blue-500'\n                                            },\n                                            {\n                                                source: '粉丝打赏',\n                                                amount: 15000,\n                                                percentage: 29,\n                                                color: 'bg-yellow-500'\n                                            },\n                                            {\n                                                source: '广告分成',\n                                                amount: 6000,\n                                                percentage: 12,\n                                                color: 'bg-green-500'\n                                            },\n                                            {\n                                                source: '直播收益',\n                                                amount: 3000,\n                                                percentage: 5,\n                                                color: 'bg-purple-500'\n                                            }\n                                        ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/5 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm\",\n                                                                children: item.source\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1383,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 text-sm font-medium\",\n                                                                children: [\n                                                                    \"\\xa5\",\n                                                                    formatNumber(item.amount)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1384,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\".concat(item.color, \" h-2 rounded-full transition-all duration-500\"),\n                                                            style: {\n                                                                width: \"\".concat(item.percentage, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 1387,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1386,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            item.percentage,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1392,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1381,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1374,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1372,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-3\",\n                                        children: \"等级权益\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1400,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded text-xs font-bold text-black\",\n                                                        children: currentContent.economy.tier\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1403,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-300 text-sm\",\n                                                        children: \"等级权益\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1406,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1402,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm text-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1410,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"优先推荐位置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1411,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1409,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1414,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"专属客服支持\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1415,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1413,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1418,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"高级数据分析\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1419,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1417,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1422,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"NFT铸造优惠\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1423,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1421,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1408,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1401,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1399,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-3\",\n                                        children: \"收益趋势\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1431,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-end space-x-2 h-20\",\n                                                children: [\n                                                    40,\n                                                    65,\n                                                    45,\n                                                    80,\n                                                    60,\n                                                    90,\n                                                    75\n                                                ].map((height, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gradient-to-t from-green-500 to-emerald-400 rounded-t\",\n                                                                style: {\n                                                                    height: \"\".concat(height, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1436,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: [\n                                                                    index + 1,\n                                                                    \"月\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1440,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1435,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1433,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"近7个月收益趋势\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1445,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1444,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1432,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1430,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowEconomyModal(false),\n                                className: \"w-full py-3 bg-gray-700 rounded-xl text-white font-medium\",\n                                children: \"关闭\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1450,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 1342,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                    lineNumber: 1335,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 1333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: showLevelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                    onClick: ()=>setShowLevelModal(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-yellow-500/30 max-h-[80vh] overflow-y-auto\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-4\",\n                                        children: \"\\uD83D\\uDC51\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1479,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-white mb-2\",\n                                        children: \"创作者等级系统\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1480,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"了解不同等级的权益和要求\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1481,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1478,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-2\",\n                                            children: \"\\uD83D\\uDC51\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 1487,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full text-sm font-bold text-black inline-block mb-2\",\n                                            children: currentContent.creator.level\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 1488,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-medium\",\n                                            children: \"当前等级\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 1491,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-xs mt-1\",\n                                            children: \"顶级创作者，平台最高荣誉\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 1492,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 1486,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1485,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    {\n                                        level: 'Legend',\n                                        icon: '👑',\n                                        name: '传奇',\n                                        requirement: '月收入 ≥ 50k',\n                                        benefits: [\n                                            '专属推荐位',\n                                            '优先客服',\n                                            '高级分析',\n                                            'NFT优惠'\n                                        ],\n                                        color: 'from-yellow-400 to-orange-500',\n                                        current: currentContent.creator.level === 'Legend'\n                                    },\n                                    {\n                                        level: 'Elite',\n                                        icon: '⭐',\n                                        name: '精英',\n                                        requirement: '月收入 ≥ 20k',\n                                        benefits: [\n                                            '推荐加权',\n                                            '数据分析',\n                                            '活动优先',\n                                            '认证加速'\n                                        ],\n                                        color: 'from-purple-500 to-indigo-500',\n                                        current: currentContent.creator.level === 'Elite'\n                                    },\n                                    {\n                                        level: 'Popular',\n                                        icon: '🔥',\n                                        name: '热门',\n                                        requirement: '月收入 ≥ 5k',\n                                        benefits: [\n                                            '流量扶持',\n                                            '基础分析',\n                                            '社区权限',\n                                            '活动参与'\n                                        ],\n                                        color: 'from-red-500 to-pink-500',\n                                        current: currentContent.creator.level === 'Popular'\n                                    },\n                                    {\n                                        level: 'Rising',\n                                        icon: '🌟',\n                                        name: '新星',\n                                        requirement: '月收入 ≥ 1k',\n                                        benefits: [\n                                            '新人扶持',\n                                            '基础工具',\n                                            '学习资源',\n                                            '社区支持'\n                                        ],\n                                        color: 'from-blue-500 to-cyan-500',\n                                        current: currentContent.creator.level === 'Rising'\n                                    }\n                                ].map((levelInfo, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-lg p-4 border \".concat(levelInfo.current ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-500/50' : 'bg-white/5 border-white/10'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl\",\n                                                        children: levelInfo.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1542,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-2 py-1 bg-gradient-to-r \".concat(levelInfo.color, \" rounded text-xs font-bold text-black inline-block\"),\n                                                                children: levelInfo.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1544,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs mt-1\",\n                                                                children: levelInfo.requirement\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1547,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1543,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    levelInfo.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-green-500/20 text-green-400 rounded-full text-xs border border-green-500/30\",\n                                                            children: \"当前\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                            lineNumber: 1551,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1550,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1541,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-2\",\n                                                children: levelInfo.benefits.map((benefit, benefitIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 text-xs\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1560,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300 text-xs\",\n                                                                children: benefit\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                                lineNumber: 1561,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, benefitIndex, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                        lineNumber: 1559,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                lineNumber: 1557,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                        lineNumber: 1536,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1497,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-200 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium mb-2\",\n                                            children: \"\\uD83D\\uDCA1 升级提示\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 1572,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• 等级基于月收入和创作质量综合评定\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1574,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• 每月1号更新等级，连续3个月达标可升级\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1575,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• 高等级享受更多平台资源和收益分成\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1576,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• 违规行为可能导致等级降低\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                                    lineNumber: 1577,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                            lineNumber: 1573,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                    lineNumber: 1571,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1570,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowLevelModal(false),\n                                className: \"w-full py-3 bg-gray-700 rounded-xl text-white font-medium\",\n                                children: \"关闭\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                                lineNumber: 1582,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                        lineNumber: 1471,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                    lineNumber: 1464,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n                lineNumber: 1462,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\creator-economy-homepage.tsx\",\n        lineNumber: 258,\n        columnNumber: 5\n    }, this);\n}\n_s(CreatorEconomyHomepage, \"N3gzvGw5ri75ldvDxZiuA09XgGI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CreatorEconomyHomepage;\nvar _c;\n$RefreshReg$(_c, \"CreatorEconomyHomepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/creator-economy-homepage.tsx\n"));

/***/ })

});