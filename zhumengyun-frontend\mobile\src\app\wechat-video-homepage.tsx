'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'

export default function WeChatVideoHomepage() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLiked, setIsLiked] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [showComments, setShowComments] = useState(false)
  const [showShare, setShowShare] = useState(false)
  const [isPlaying, setIsPlaying] = useState(true)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(60)
  const [activeTab, setActiveTab] = useState('关注') // 关注/朋友切换

  // 工程视频内容数据 - 根据图片风格设计
  const videoContent = [
    {
      id: 1,
      title: "何兰风车+龙猫烤炉！邻居连夜举报......",
      author: "娟小刘J7",
      authorAvatar: "🏗️",
      verified: false,
      description: "何兰风车+龙猫烤炉！邻居连夜举报......",
      videoUrl: "/api/placeholder/400/700",
      likes: 94000,
      comments: 100000,
      shares: 34000,
      tags: ["#工程建设", "#创意设计", "#邻里故事"],
      location: "浙江省杭州市",
      music: "原创音乐 - 工地进行曲",
      videoTime: "10:52",
      friendCount: "1个朋友关注"
    },
    {
      id: 2,
      title: "智慧工地AI监控系统实时演示",
      author: "工程师老王",
      authorAvatar: "👷",
      verified: true,
      description: "🏗️ 最新AI技术在建筑工地的应用，实时监控施工安全，提升工程效率。",
      videoUrl: "/api/placeholder/400/700",
      likes: 156000,
      comments: 89000,
      shares: 45000,
      tags: ["#智慧工地", "#AI监控", "#安全施工"],
      location: "上海市浦东新区",
      music: "电子音乐 - 科技未来",
      videoTime: "08:30",
      friendCount: "3个朋友关注"
    },
    {
      id: 3,
      title: "超级工程：跨海大桥建设全过程",
      author: "桥梁专家",
      authorAvatar: "🌉",
      verified: true,
      description: "🌊 见证跨海大桥从设计到建成的震撼过程，工程技术的巅峰之作。",
      videoUrl: "/api/placeholder/400/700",
      likes: 234000,
      comments: 156000,
      shares: 78000,
      tags: ["#超级工程", "#跨海大桥", "#建筑奇迹"],
      location: "广东省珠海市",
      music: "交响乐 - 建设者之歌",
      videoTime: "15:20",
      friendCount: "8个朋友关注"
    }
  ]

  const currentContent = videoContent[currentIndex]

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  }

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 交互处理函数
  const handleLike = useCallback(() => setIsLiked(!isLiked), [isLiked])
  const handleFollow = useCallback(() => setIsFollowing(!isFollowing), [isFollowing])
  const handleComment = useCallback(() => setShowComments(true), [])
  const handleShare = useCallback(() => setShowShare(true), [])
  const togglePlay = useCallback(() => setIsPlaying(!isPlaying), [isPlaying])

  // 视频切换函数
  const nextVideo = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % videoContent.length)
    setIsLiked(false)
    setIsFollowing(false)
    setIsPlaying(true)
    setCurrentTime(0)
  }, [videoContent.length])

  const prevVideo = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + videoContent.length) % videoContent.length)
    setIsLiked(false)
    setIsFollowing(false)
    setIsPlaying(true)
    setCurrentTime(0)
  }, [videoContent.length])

  // 触摸手势处理
  const [touchStart, setTouchStart] = useState(0)
  const [touchEnd, setTouchEnd] = useState(0)

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientY)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientY)
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    const distance = touchStart - touchEnd
    const isUpSwipe = distance > 50
    const isDownSwipe = distance < -50

    if (isUpSwipe) {
      nextVideo()
    }
    if (isDownSwipe) {
      prevVideo()
    }
  }

  // 自动播放进度
  useEffect(() => {
    if (isPlaying) {
      const timer = setInterval(() => {
        setCurrentTime(prev => {
          if (prev >= duration) {
            nextVideo()
            return 0
          }
          return prev + 1
        })
      }, 1000)
      return () => clearInterval(timer)
    }
  }, [isPlaying, duration, nextVideo])

  return (
    <div className="h-screen bg-black relative overflow-hidden">
      {/* 视频背景 - 模拟工程施工场景 */}
      <div
        className="absolute inset-0"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onClick={togglePlay}
      >
        {/* 工程施工视频内容 */}
        <div className="w-full h-full relative">
          {/* 背景图片/视频区域 */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-600 via-gray-700 to-gray-800">
            {/* 模拟工程施工场景 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-full h-full relative">
                {/* 施工现场背景 */}
                <div className="absolute inset-0 bg-gradient-to-b from-gray-500/20 to-gray-900/60"></div>

                {/* 施工内容展示 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="w-32 h-32 bg-orange-500/30 rounded-full flex items-center justify-center mb-4 mx-auto backdrop-blur-sm border-2 border-orange-400/50">
                      <span className="text-5xl">{currentContent.authorAvatar}</span>
                    </div>
                    <div className="bg-black/40 backdrop-blur-sm rounded-lg p-4 mx-4">
                      <h3 className="text-lg font-bold mb-2">{currentContent.title}</h3>
                      <p className="text-sm opacity-90">{currentContent.description}</p>
                    </div>
                  </div>
                </div>

                {/* 施工烟雾效果 */}
                <div className="absolute bottom-20 left-1/4 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
                <div className="absolute bottom-32 right-1/3 w-24 h-24 bg-white/5 rounded-full blur-lg animate-pulse delay-1000"></div>
              </div>
            </div>
          </div>

          {/* 播放暂停指示器 */}
          {!isPlaying && (
            <div className="absolute inset-0 flex items-center justify-center z-20">
              <div className="w-20 h-20 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm">
                <div className="w-0 h-0 border-l-[20px] border-l-white border-y-[12px] border-y-transparent ml-1"></div>
              </div>
            </div>
          )}

          {/* 视频时长显示 */}
          <div className="absolute bottom-4 right-4 bg-black/60 backdrop-blur-sm rounded-lg px-2 py-1 z-10">
            <span className="text-white text-sm font-medium">{currentContent.videoTime}</span>
          </div>
        </div>
      </div>

      {/* 顶部导航栏 - 仿抖音风格 */}
      <div className="absolute top-0 left-0 right-0 z-30 pt-12 pb-4">
        <div className="flex items-center justify-center space-x-8">
          <button
            onClick={() => setActiveTab('关注')}
            className={`text-lg font-medium transition-all duration-200 ${
              activeTab === '关注'
                ? 'text-white border-b-2 border-white pb-1'
                : 'text-white/60'
            }`}
          >
            关注
          </button>
          <button
            onClick={() => setActiveTab('朋友')}
            className={`text-lg font-medium transition-all duration-200 flex items-center space-x-1 ${
              activeTab === '朋友'
                ? 'text-white border-b-2 border-white pb-1'
                : 'text-white/60'
            }`}
          >
            <span>朋友</span>
            <span className="text-red-500">♥</span>
          </button>
        </div>

        {/* 右上角功能按钮 */}
        <div className="absolute top-12 right-4 flex items-center space-x-4">
          <button className="w-8 h-8 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center">
            <span className="text-white text-lg">🔍</span>
          </button>
          <button className="w-8 h-8 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center">
            <span className="text-white text-lg">📷</span>
          </button>
        </div>
      </div>

      {/* 右侧交互按钮 - 抖音风格 */}
      <div className="absolute right-3 bottom-24 flex flex-col items-center space-y-4 z-30">
        {/* 作者头像 */}
        <div className="relative">
          <div className="w-14 h-14 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 p-0.5">
            <div className="w-full h-full rounded-full bg-gray-800 flex items-center justify-center">
              <span className="text-2xl">{currentContent.authorAvatar}</span>
            </div>
          </div>
          {!isFollowing && (
            <motion.button
              onClick={handleFollow}
              whileTap={{ scale: 0.8 }}
              className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center border-2 border-white"
            >
              <span className="text-white text-xs font-bold">+</span>
            </motion.button>
          )}
        </div>

        {/* 点赞 */}
        <motion.button
          onClick={handleLike}
          whileTap={{ scale: 0.8 }}
          className="flex flex-col items-center space-y-1"
        >
          <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-all ${
            isLiked ? 'bg-red-500/20' : 'bg-black/20 backdrop-blur-sm'
          }`}>
            <motion.div
              animate={isLiked ? { scale: [1, 1.3, 1] } : {}}
              transition={{ duration: 0.4 }}
              className="text-2xl"
            >
              {isLiked ? '❤️' : '🤍'}
            </motion.div>
          </div>
          <span className="text-white text-xs font-medium">
            {formatNumber(currentContent.likes + (isLiked ? 1 : 0))}
          </span>
        </motion.button>

        {/* 评论 */}
        <button
          onClick={handleComment}
          className="flex flex-col items-center space-y-1"
        >
          <div className="w-12 h-12 rounded-full bg-black/20 backdrop-blur-sm flex items-center justify-center">
            <span className="text-2xl">💬</span>
          </div>
          <span className="text-white text-xs font-medium">{formatNumber(currentContent.comments)}</span>
        </button>

        {/* 分享 */}
        <button
          onClick={handleShare}
          className="flex flex-col items-center space-y-1"
        >
          <div className="w-12 h-12 rounded-full bg-black/20 backdrop-blur-sm flex items-center justify-center">
            <span className="text-2xl">↗️</span>
          </div>
          <span className="text-white text-xs font-medium">{formatNumber(currentContent.shares)}</span>
        </button>

        {/* 音乐图标 */}
        <div className="flex flex-col items-center space-y-1">
          <motion.div
            animate={{ rotate: isPlaying ? 360 : 0 }}
            transition={{ duration: 4, repeat: isPlaying ? Infinity : 0, ease: "linear" }}
            className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center border-2 border-white/50"
          >
            <span className="text-xl">🎵</span>
          </motion.div>
        </div>
      </div>

      {/* 底部信息区域 - 抖音风格 */}
      <div className="absolute bottom-20 left-0 right-16 p-4 z-20">
        {/* 作者信息和关注状态 */}
        <div className="flex items-center space-x-3 mb-3">
          <div className="flex items-center space-x-2">
            <span className="text-white font-bold text-lg">{currentContent.author}</span>
            {currentContent.verified && (
              <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
            )}
          </div>
          <span className="text-gray-300 text-sm">{currentContent.friendCount}</span>
        </div>

        {/* 视频标题/描述 */}
        <p className="text-white text-base mb-3 leading-relaxed font-medium">
          {currentContent.title}
        </p>

        {/* 标签 */}
        <div className="flex flex-wrap gap-2 mb-3">
          {currentContent.tags.map((tag, index) => (
            <span key={index} className="text-white text-sm bg-black/30 backdrop-blur-sm px-2 py-1 rounded-full">
              {tag}
            </span>
          ))}
        </div>

        {/* 音乐信息 */}
        <div className="flex items-center space-x-2 mb-4">
          <motion.div
            animate={{ rotate: isPlaying ? 360 : 0 }}
            transition={{ duration: 3, repeat: isPlaying ? Infinity : 0, ease: "linear" }}
            className="w-4 h-4 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center"
          >
            <span className="text-xs">🎵</span>
          </motion.div>
          <span className="text-white text-sm opacity-90">{currentContent.music}</span>
        </div>

        {/* 位置信息 */}
        <div className="flex items-center space-x-2">
          <span className="text-white text-xs">📍</span>
          <span className="text-white text-xs opacity-80">{currentContent.location}</span>
        </div>
      </div>

      {/* 评论弹窗 */}
      <AnimatePresence>
        {showComments && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-end"
            onClick={() => setShowComments(false)}
          >
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              className="w-full bg-white rounded-t-3xl max-h-[70vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* 评论头部 */}
              <div className="flex items-center justify-between p-4 border-b border-gray-100">
                <h3 className="text-lg font-semibold text-gray-900">
                  {formatNumber(currentContent.comments)}条评论
                </h3>
                <button
                  onClick={() => setShowComments(false)}
                  className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center"
                >
                  <span className="text-gray-500">✕</span>
                </button>
              </div>

              {/* 评论列表 */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {[
                  { user: '工程师老李', avatar: '👷', content: '这个创意太有趣了，邻居举报是因为嫉妒吧哈哈', time: '2分钟前', likes: 1280 },
                  { user: '建筑设计师', avatar: '🏗️', content: '龙猫烤炉的设计很有想象力，但要注意安全规范', time: '5分钟前', likes: 890 },
                  { user: '邻居大妈', avatar: '👵', content: '确实有点吵，但是很有创意，支持！', time: '8分钟前', likes: 567 },
                  { user: '工地小王', avatar: '⛑️', content: '哈哈哈，我们工地也想搞一个', time: '10分钟前', likes: 345 },
                  { user: '安全监督员', avatar: '🦺', content: '创意不错，但要符合建筑安全标准哦', time: '15分钟前', likes: 234 }
                ].map((comment, index) => (
                  <div key={index} className="flex space-x-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center flex-shrink-0">
                      <span className="text-white">{comment.avatar}</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-gray-900">{comment.user}</span>
                        <span className="text-gray-500 text-sm">{comment.time}</span>
                      </div>
                      <p className="text-gray-700 mb-2">{comment.content}</p>
                      <div className="flex items-center space-x-4">
                        <button className="flex items-center space-x-1 text-gray-500 hover:text-red-500">
                          <span className="text-sm">❤️</span>
                          <span className="text-sm">{comment.likes}</span>
                        </button>
                        <button className="text-gray-500 hover:text-blue-500 text-sm">回复</button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 评论输入 */}
              <div className="p-4 border-t border-gray-100 bg-gray-50">
                <div className="flex space-x-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm">我</span>
                  </div>
                  <div className="flex-1 flex space-x-2">
                    <input
                      type="text"
                      placeholder="说点什么..."
                      className="flex-1 bg-white border border-gray-200 rounded-full px-4 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-blue-500"
                    />
                    <button className="bg-blue-500 hover:bg-blue-600 rounded-full px-6 py-2 text-white font-medium">
                      发送
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 分享弹窗 */}
      <AnimatePresence>
        {showShare && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-end"
            onClick={() => setShowShare(false)}
          >
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              className="w-full bg-white rounded-t-3xl p-6"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">分享到</h3>
                <button
                  onClick={() => setShowShare(false)}
                  className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center"
                >
                  <span className="text-gray-500">✕</span>
                </button>
              </div>

              <div className="grid grid-cols-4 gap-6 mb-8">
                {[
                  { name: '微信好友', icon: '💬', color: 'bg-green-500' },
                  { name: '朋友圈', icon: '🌟', color: 'bg-green-600' },
                  { name: '工程群', icon: '👷', color: 'bg-orange-500' },
                  { name: '项目组', icon: '🏗️', color: 'bg-blue-600' },
                  { name: '微博', icon: '📱', color: 'bg-red-500' },
                  { name: '抖音', icon: '🎵', color: 'bg-black' },
                  { name: '复制链接', icon: '🔗', color: 'bg-gray-600' },
                  { name: '保存视频', icon: '📥', color: 'bg-purple-500' }
                ].map((platform, index) => (
                  <button
                    key={index}
                    className="flex flex-col items-center space-y-2"
                  >
                    <div className={`w-14 h-14 ${platform.color} rounded-2xl flex items-center justify-center`}>
                      <span className="text-white text-xl">{platform.icon}</span>
                    </div>
                    <span className="text-gray-700 text-sm text-center">{platform.name}</span>
                  </button>
                ))}
              </div>

              <button
                onClick={() => setShowShare(false)}
                className="w-full py-4 bg-gray-100 hover:bg-gray-200 rounded-2xl text-gray-700 font-medium"
              >
                取消
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
