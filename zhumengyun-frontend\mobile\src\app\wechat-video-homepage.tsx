'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'

export default function WeChatVideoHomepage() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLiked, setIsLiked] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [showComments, setShowComments] = useState(false)
  const [showShare, setShowShare] = useState(false)
  const [isPlaying, setIsPlaying] = useState(true)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(60)
  const [activeTab, setActiveTab] = useState('推荐') // 推荐/关注/朋友切换

  // 推荐视频内容数据
  const recommendedContent = [
    {
      id: 1,
      title: "震撼！全球最高摩天大楼建设纪录片",
      author: "建筑奇迹",
      authorAvatar: "🏢",
      verified: true,
      description: "🏗️ 见证828米迪拜塔的建设全过程，工程技术的巅峰之作！",
      videoUrl: "/api/placeholder/400/700",
      likes: 1200000,
      comments: 89000,
      shares: 156000,
      tags: ["#摩天大楼", "#建筑奇迹", "#工程技术"],
      location: "阿联酋迪拜",
      music: "史诗音乐 - 建设者之歌",
      videoTime: "12:30",
      friendCount: "热门推荐"
    },
    {
      id: 2,
      title: "AI机器人自动化建房，24小时完工！",
      author: "未来建筑师",
      authorAvatar: "🤖",
      verified: true,
      description: "🚀 革命性的3D打印建筑技术，机器人24小时自动建房全过程！",
      videoUrl: "/api/placeholder/400/700",
      likes: 890000,
      comments: 67000,
      shares: 123000,
      tags: ["#AI建筑", "#3D打印", "#未来科技"],
      location: "美国硅谷",
      music: "科技音乐 - 未来之声",
      videoTime: "08:45",
      friendCount: "热门推荐"
    },
    {
      id: 3,
      title: "中国基建狂魔：高铁穿山越岭全纪录",
      author: "基建达人",
      authorAvatar: "🚄",
      verified: true,
      description: "🚄 中国高铁建设的震撼场面，穿山越岭的工程奇迹！",
      videoUrl: "/api/placeholder/400/700",
      likes: 1560000,
      comments: 234000,
      shares: 289000,
      tags: ["#中国基建", "#高铁建设", "#工程奇迹"],
      location: "中国四川",
      music: "磅礴音乐 - 中华力量",
      videoTime: "15:20",
      friendCount: "热门推荐"
    }
  ]

  // 关注视频内容数据
  const followingContent = [
    {
      id: 1,
      title: "何兰风车+龙猫烤炉！邻居连夜举报......",
      author: "娟小刘J7",
      authorAvatar: "🏗️",
      verified: false,
      description: "何兰风车+龙猫烤炉！邻居连夜举报......",
      videoUrl: "/api/placeholder/400/700",
      likes: 94000,
      comments: 100000,
      shares: 34000,
      tags: ["#工程建设", "#创意设计", "#邻里故事"],
      location: "浙江省杭州市",
      music: "原创音乐 - 工地进行曲",
      videoTime: "10:52",
      friendCount: "1个朋友关注"
    },
    {
      id: 2,
      title: "智慧工地AI监控系统实时演示",
      author: "工程师老王",
      authorAvatar: "👷",
      verified: true,
      description: "🏗️ 最新AI技术在建筑工地的应用，实时监控施工安全，提升工程效率。",
      videoUrl: "/api/placeholder/400/700",
      likes: 156000,
      comments: 89000,
      shares: 45000,
      tags: ["#智慧工地", "#AI监控", "#安全施工"],
      location: "上海市浦东新区",
      music: "电子音乐 - 科技未来",
      videoTime: "08:30",
      friendCount: "3个朋友关注"
    },
    {
      id: 3,
      title: "超级工程：跨海大桥建设全过程",
      author: "桥梁专家",
      authorAvatar: "🌉",
      verified: true,
      description: "🌊 见证跨海大桥从设计到建成的震撼过程，工程技术的巅峰之作。",
      videoUrl: "/api/placeholder/400/700",
      likes: 234000,
      comments: 156000,
      shares: 78000,
      tags: ["#超级工程", "#跨海大桥", "#建筑奇迹"],
      location: "广东省珠海市",
      music: "交响乐 - 建设者之歌",
      videoTime: "15:20",
      friendCount: "8个朋友关注"
    }
  ]

  // 朋友视频内容数据
  const friendsContent = [
    {
      id: 1,
      title: "我家装修日记第30天，终于看到希望了！",
      author: "装修小白",
      authorAvatar: "🏠",
      verified: false,
      description: "💪 历时一个月的装修终于有起色了，分享一下心得体会！",
      videoUrl: "/api/placeholder/400/700",
      likes: 12000,
      comments: 890,
      shares: 456,
      tags: ["#装修日记", "#家居设计", "#生活分享"],
      location: "北京市朝阳区",
      music: "轻音乐 - 温馨家园",
      videoTime: "06:30",
      friendCount: "5个朋友关注"
    },
    {
      id: 2,
      title: "周末和朋友一起DIY小花园",
      author: "园艺爱好者",
      authorAvatar: "🌱",
      verified: false,
      description: "🌸 和好朋友一起打造小花园，享受美好的周末时光！",
      videoUrl: "/api/placeholder/400/700",
      likes: 8900,
      comments: 567,
      shares: 234,
      tags: ["#DIY花园", "#周末时光", "#朋友聚会"],
      location: "上海市徐汇区",
      music: "田园音乐 - 春天的故事",
      videoTime: "09:15",
      friendCount: "12个朋友关注"
    },
    {
      id: 3,
      title: "爸爸亲手给我做的小木屋完工啦！",
      author: "小小建筑师",
      authorAvatar: "🏘️",
      verified: false,
      description: "👨‍👧 爸爸用了两个月时间给我做的小木屋终于完工了，太感动了！",
      videoUrl: "/api/placeholder/400/700",
      likes: 45000,
      comments: 2300,
      shares: 1200,
      tags: ["#父女情深", "#手工制作", "#小木屋"],
      location: "成都市武侯区",
      music: "温情音乐 - 父爱如山",
      videoTime: "11:40",
      friendCount: "18个朋友关注"
    }
  ]

  // 根据当前标签获取对应的内容
  const getCurrentContent = () => {
    switch (activeTab) {
      case '推荐':
        return recommendedContent
      case '关注':
        return followingContent
      case '朋友':
        return friendsContent
      default:
        return recommendedContent
    }
  }

  const videoContent = getCurrentContent()
  const currentContent = videoContent[currentIndex]



  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  }

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 交互处理函数
  const handleLike = useCallback(() => setIsLiked(!isLiked), [isLiked])
  const handleFollow = useCallback(() => setIsFollowing(!isFollowing), [isFollowing])
  const handleComment = useCallback(() => setShowComments(true), [])
  const handleShare = useCallback(() => setShowShare(true), [])
  const togglePlay = useCallback(() => setIsPlaying(!isPlaying), [isPlaying])

  // 标签切换处理函数
  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab)
    setCurrentIndex(0) // 切换标签时重置到第一个视频
    setIsLiked(false)
    setIsFollowing(false)
    setIsPlaying(true)
    setCurrentTime(0)
  }, [])

  // 视频切换函数
  const nextVideo = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % videoContent.length)
    setIsLiked(false)
    setIsFollowing(false)
    setIsPlaying(true)
    setCurrentTime(0)
  }, [videoContent.length])

  const prevVideo = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + videoContent.length) % videoContent.length)
    setIsLiked(false)
    setIsFollowing(false)
    setIsPlaying(true)
    setCurrentTime(0)
  }, [videoContent.length])

  // 触摸手势处理
  const [touchStart, setTouchStart] = useState(0)
  const [touchEnd, setTouchEnd] = useState(0)

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientY)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientY)
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    const distance = touchStart - touchEnd
    const isUpSwipe = distance > 50
    const isDownSwipe = distance < -50

    if (isUpSwipe) {
      nextVideo()
    }
    if (isDownSwipe) {
      prevVideo()
    }
  }

  // 自动播放进度
  useEffect(() => {
    if (isPlaying) {
      const timer = setInterval(() => {
        setCurrentTime(prev => {
          if (prev >= duration) {
            nextVideo()
            return 0
          }
          return prev + 1
        })
      }, 1000)
      return () => clearInterval(timer)
    }
  }, [isPlaying, duration, nextVideo])

  return (
    <div className="h-screen bg-gray-800 relative overflow-hidden">
      {/* 主要内容区域 - 适应固定导航栏 */}
      <div
        className="absolute inset-0 pt-20 pb-32"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onClick={togglePlay}
      >
        {/* 工程施工视频内容 */}
        <div className="w-full h-full relative px-4">
          {/* 视频展示区域 - 圆角设计融合背景 */}
          <div className="w-full h-full bg-gray-700 rounded-2xl overflow-hidden relative">
            <div className="absolute inset-0 bg-gradient-to-br from-gray-600 via-gray-700 to-gray-800">
            {/* 模拟工程施工场景 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-full h-full relative">
                {/* 施工现场背景 */}
                <div className="absolute inset-0 bg-gradient-to-b from-gray-500/20 to-gray-900/60"></div>

                {/* 施工内容展示 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="w-32 h-32 bg-orange-500/30 rounded-full flex items-center justify-center mb-4 mx-auto backdrop-blur-sm border-2 border-orange-400/50">
                      <span className="text-5xl">{currentContent.authorAvatar}</span>
                    </div>
                    <div className="bg-black/40 backdrop-blur-sm rounded-lg p-4 mx-4">
                      <h3 className="text-lg font-bold mb-2">{currentContent.title}</h3>
                      <p className="text-sm opacity-90">{currentContent.description}</p>
                    </div>
                  </div>
                </div>

                {/* 施工烟雾效果 */}
                <div className="absolute bottom-20 left-1/4 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
                <div className="absolute bottom-32 right-1/3 w-24 h-24 bg-white/5 rounded-full blur-lg animate-pulse delay-1000"></div>
              </div>
            </div>
          </div>

          {/* 播放暂停指示器 */}
          {!isPlaying && (
            <div className="absolute inset-0 flex items-center justify-center z-20">
              <div className="w-20 h-20 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm">
                <div className="w-0 h-0 border-l-[20px] border-l-white border-y-[12px] border-y-transparent ml-1"></div>
              </div>
            </div>
          )}
          </div>
        </div>
      </div>

      {/* 顶部导航栏 - 固定不动，统一灰色背景 */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center justify-center px-4 pt-12 pb-4">
          {/* 中间标签切换 */}
          <div className="flex items-center space-x-8">
            <button
              onClick={() => handleTabChange('推荐')}
              className={`text-lg font-medium transition-all duration-200 relative ${
                activeTab === '推荐'
                  ? 'text-white'
                  : 'text-white/70'
              }`}
            >
              推荐
              {activeTab === '推荐' && (
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full"></div>
              )}
            </button>
            <button
              onClick={() => handleTabChange('关注')}
              className={`text-lg font-medium transition-all duration-200 relative ${
                activeTab === '关注'
                  ? 'text-white'
                  : 'text-white/70'
              }`}
            >
              关注
              {activeTab === '关注' && (
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full"></div>
              )}
            </button>
            <button
              onClick={() => handleTabChange('朋友')}
              className={`text-lg font-medium transition-all duration-200 flex items-center space-x-1 relative ${
                activeTab === '朋友'
                  ? 'text-white'
                  : 'text-white/70'
              }`}
            >
              <span>朋友</span>
              <span className="text-red-500 text-sm">♥</span>
              {activeTab === '朋友' && (
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full"></div>
              )}
            </button>
          </div>

          {/* 右上角功能按钮 */}
          <div className="absolute right-4 top-12 flex items-center space-x-3">
            <button className="w-8 h-8 flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
            <button className="w-8 h-8 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
              <div className="w-1 h-1 bg-white rounded-full mx-0.5"></div>
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </button>
          </div>
        </div>
      </div>

      {/* 视频时间显示 - 固定位置 */}
      <div className="fixed top-16 right-4 bg-black/60 backdrop-blur-sm rounded-lg px-2 py-1 z-40">
        <span className="text-white text-sm font-medium">{currentContent.videoTime}</span>
      </div>

      {/* 底部功能区域 - 完全按照图片效果设计，统一灰色背景 */}
      <div className="fixed bottom-20 left-0 right-0 z-40">
        <div className="bg-gray-800 border-t border-gray-700 px-4 py-3">
          <div className="flex items-center justify-between">
            {/* 左侧用户信息 */}
            <div className="flex items-center space-x-3">
              {/* 用户头像 */}
              <div className="w-12 h-12 rounded-full overflow-hidden">
                <div className="w-full h-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                  <span className="text-white text-lg">{currentContent.authorAvatar}</span>
                </div>
              </div>

              {/* 用户信息 */}
              <div className="flex flex-col">
                <div className="flex items-center space-x-1">
                  <span className="text-white font-medium text-base">{currentContent.author}</span>
                  {currentContent.verified && (
                    <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  )}
                </div>
                <span className="text-gray-300 text-sm">{currentContent.friendCount}</span>
              </div>
            </div>

            {/* 右侧交互按钮 */}
            <div className="flex items-center space-x-6">
              {/* 分享 */}
              <button
                onClick={handleShare}
                className="flex flex-col items-center"
              >
                <div className="w-8 h-8 flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
                  </svg>
                </div>
                <span className="text-white text-xs">{formatNumber(currentContent.shares)}</span>
              </button>

              {/* 点赞 */}
              <motion.button
                onClick={handleLike}
                whileTap={{ scale: 0.8 }}
                className="flex flex-col items-center"
              >
                <div className="w-8 h-8 flex items-center justify-center">
                  <motion.div
                    animate={isLiked ? { scale: [1, 1.2, 1] } : {}}
                    transition={{ duration: 0.3 }}
                  >
                    {isLiked ? (
                      <svg className="w-6 h-6 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                      </svg>
                    ) : (
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                      </svg>
                    )}
                  </motion.div>
                </div>
                <span className="text-white text-xs">
                  {formatNumber(currentContent.likes + (isLiked ? 1 : 0))}
                </span>
              </motion.button>

              {/* 评论 */}
              <button
                onClick={handleComment}
                className="flex flex-col items-center"
              >
                <div className="w-8 h-8 flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                  </svg>
                </div>
                <span className="text-white text-xs">{formatNumber(currentContent.comments)}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 评论弹窗 */}
      <AnimatePresence>
        {showComments && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-end"
            onClick={() => setShowComments(false)}
          >
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              className="w-full bg-white rounded-t-3xl max-h-[70vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* 评论头部 */}
              <div className="flex items-center justify-between p-4 border-b border-gray-100">
                <h3 className="text-lg font-semibold text-gray-900">
                  {formatNumber(currentContent.comments)}条评论
                </h3>
                <button
                  onClick={() => setShowComments(false)}
                  className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center"
                >
                  <span className="text-gray-500">✕</span>
                </button>
              </div>

              {/* 评论列表 */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {(() => {
                  // 根据当前标签和视频内容显示不同的评论
                  const getComments = () => {
                    if (activeTab === '推荐') {
                      return [
                        { user: '建筑学教授', avatar: '👨‍🏫', content: '这种超高层建筑的施工技术确实令人震撼！', time: '1分钟前', likes: 2580 },
                        { user: '结构工程师', avatar: '🏗️', content: '钢结构的连接工艺太精湛了，学到了很多', time: '3分钟前', likes: 1890 },
                        { user: '建筑爱好者', avatar: '🏢', content: '人类的建筑智慧真是无穷无尽', time: '5分钟前', likes: 1567 },
                        { user: '工程管理师', avatar: '📋', content: '项目管理的复杂度可想而知', time: '8分钟前', likes: 1245 },
                        { user: '安全专家', avatar: '🦺', content: '高空作业的安全措施做得很到位', time: '12分钟前', likes: 934 }
                      ]
                    } else if (activeTab === '关注') {
                      return [
                        { user: '工程师老李', avatar: '👷', content: '这个创意太有趣了，邻居举报是因为嫉妒吧哈哈', time: '2分钟前', likes: 1280 },
                        { user: '建筑设计师', avatar: '🏗️', content: '龙猫烤炉的设计很有想象力，但要注意安全规范', time: '5分钟前', likes: 890 },
                        { user: '邻居大妈', avatar: '👵', content: '确实有点吵，但是很有创意，支持！', time: '8分钟前', likes: 567 },
                        { user: '工地小王', avatar: '⛑️', content: '哈哈哈，我们工地也想搞一个', time: '10分钟前', likes: 345 },
                        { user: '安全监督员', avatar: '🦺', content: '创意不错，但要符合建筑安全标准哦', time: '15分钟前', likes: 234 }
                      ]
                    } else {
                      return [
                        { user: '装修达人', avatar: '🔨', content: '装修真的是个体力活，加油！', time: '1分钟前', likes: 89 },
                        { user: '邻居小张', avatar: '👨', content: '我家也在装修，互相学习一下', time: '3分钟前', likes: 67 },
                        { user: '设计师朋友', avatar: '🎨', content: '颜色搭配很不错，有品味！', time: '5分钟前', likes: 45 },
                        { user: '妈妈', avatar: '👩', content: '儿子加油，妈妈支持你！', time: '8分钟前', likes: 123 },
                        { user: '室友', avatar: '👫', content: '终于要完工了，期待新家！', time: '10分钟前', likes: 34 }
                      ]
                    }
                  }
                  return getComments().map((comment, index) => (
                    <div key={index} className="flex space-x-3">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center flex-shrink-0">
                        <span className="text-white">{comment.avatar}</span>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-medium text-gray-900">{comment.user}</span>
                          <span className="text-gray-500 text-sm">{comment.time}</span>
                        </div>
                        <p className="text-gray-700 mb-2">{comment.content}</p>
                        <div className="flex items-center space-x-4">
                          <button className="flex items-center space-x-1 text-gray-500 hover:text-red-500">
                            <span className="text-sm">❤️</span>
                            <span className="text-sm">{comment.likes}</span>
                          </button>
                          <button className="text-gray-500 hover:text-blue-500 text-sm">回复</button>
                        </div>
                      </div>
                    </div>
                  ))
                })()}
              </div>

              {/* 评论输入 */}
              <div className="p-4 border-t border-gray-100 bg-gray-50">
                <div className="flex space-x-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm">我</span>
                  </div>
                  <div className="flex-1 flex space-x-2">
                    <input
                      type="text"
                      placeholder="说点什么..."
                      className="flex-1 bg-white border border-gray-200 rounded-full px-4 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-blue-500"
                    />
                    <button className="bg-blue-500 hover:bg-blue-600 rounded-full px-6 py-2 text-white font-medium">
                      发送
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 分享弹窗 */}
      <AnimatePresence>
        {showShare && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-end"
            onClick={() => setShowShare(false)}
          >
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              className="w-full bg-white rounded-t-3xl p-6"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">分享到</h3>
                <button
                  onClick={() => setShowShare(false)}
                  className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center"
                >
                  <span className="text-gray-500">✕</span>
                </button>
              </div>

              <div className="grid grid-cols-4 gap-6 mb-8">
                {[
                  { name: '微信好友', icon: '💬', color: 'bg-green-500' },
                  { name: '朋友圈', icon: '🌟', color: 'bg-green-600' },
                  { name: '工程群', icon: '👷', color: 'bg-orange-500' },
                  { name: '项目组', icon: '🏗️', color: 'bg-blue-600' },
                  { name: '微博', icon: '📱', color: 'bg-red-500' },
                  { name: '抖音', icon: '🎵', color: 'bg-black' },
                  { name: '复制链接', icon: '🔗', color: 'bg-gray-600' },
                  { name: '保存视频', icon: '📥', color: 'bg-purple-500' }
                ].map((platform, index) => (
                  <button
                    key={index}
                    className="flex flex-col items-center space-y-2"
                  >
                    <div className={`w-14 h-14 ${platform.color} rounded-2xl flex items-center justify-center`}>
                      <span className="text-white text-xl">{platform.icon}</span>
                    </div>
                    <span className="text-gray-700 text-sm text-center">{platform.name}</span>
                  </button>
                ))}
              </div>

              <button
                onClick={() => setShowShare(false)}
                className="w-full py-4 bg-gray-100 hover:bg-gray-200 rounded-2xl text-gray-700 font-medium"
              >
                取消
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
