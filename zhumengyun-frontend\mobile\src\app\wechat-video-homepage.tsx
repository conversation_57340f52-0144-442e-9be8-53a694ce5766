'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'

export default function WeChatVideoHomepage() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLiked, setIsLiked] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [showComments, setShowComments] = useState(false)
  const [showShare, setShowShare] = useState(false)
  const [isPlaying, setIsPlaying] = useState(true)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(60)

  // 视频内容数据
  const videoContent = [
    {
      id: 1,
      title: "NextGen 2025智慧城市建设项目展示",
      author: "AI建筑大师",
      authorAvatar: "/api/placeholder/50/50",
      verified: true,
      description: "🏗️ 使用AI驱动的建筑设计，创造未来智慧城市综合体。集成VR漫游、智能管理和IoT控制系统。",
      videoUrl: "/api/placeholder/400/700",
      likes: 18900,
      comments: 2480,
      shares: 1890,
      tags: ["#AI建筑", "#智慧城市", "#未来设计"],
      location: "深圳市南山区",
      music: "原创音乐 - 未来之城"
    },
    {
      id: 2,
      title: "元宇宙虚拟展厅设计案例",
      author: "VR设计师小李",
      authorAvatar: "/api/placeholder/50/50",
      verified: true,
      description: "🌌 创新的元宇宙虚拟展厅设计，支持多人实时交互和3D展示，为企业提供沉浸式展示体验。",
      videoUrl: "/api/placeholder/400/700",
      likes: 12340,
      comments: 1567,
      shares: 890,
      tags: ["#元宇宙", "#VR设计", "#虚拟展厅"],
      location: "北京市朝阳区",
      music: "电子音乐 - 数字空间"
    },
    {
      id: 3,
      title: "AI驱动VR学习实验室",
      author: "教育科技专家",
      authorAvatar: "/api/placeholder/50/50",
      verified: true,
      description: "🎓 GPT-5个性化VR学习空间，支持物理化学生物实验和区块链认证，革新教育体验。",
      videoUrl: "/api/placeholder/400/700",
      likes: 23450,
      comments: 3210,
      shares: 1456,
      tags: ["#AI教育", "#VR学习", "#未来教育"],
      location: "上海市浦东新区",
      music: "轻音乐 - 知识之光"
    }
  ]

  const currentContent = videoContent[currentIndex]

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  }

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 交互处理函数
  const handleLike = useCallback(() => setIsLiked(!isLiked), [isLiked])
  const handleFollow = useCallback(() => setIsFollowing(!isFollowing), [isFollowing])
  const handleComment = useCallback(() => setShowComments(true), [])
  const handleShare = useCallback(() => setShowShare(true), [])
  const togglePlay = useCallback(() => setIsPlaying(!isPlaying), [isPlaying])

  // 视频切换函数
  const nextVideo = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % videoContent.length)
    setIsLiked(false)
    setIsFollowing(false)
    setIsPlaying(true)
    setCurrentTime(0)
  }, [videoContent.length])

  const prevVideo = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + videoContent.length) % videoContent.length)
    setIsLiked(false)
    setIsFollowing(false)
    setIsPlaying(true)
    setCurrentTime(0)
  }, [videoContent.length])

  // 触摸手势处理
  const [touchStart, setTouchStart] = useState(0)
  const [touchEnd, setTouchEnd] = useState(0)

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientY)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientY)
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    const distance = touchStart - touchEnd
    const isUpSwipe = distance > 50
    const isDownSwipe = distance < -50

    if (isUpSwipe) {
      nextVideo()
    }
    if (isDownSwipe) {
      prevVideo()
    }
  }

  // 自动播放进度
  useEffect(() => {
    if (isPlaying) {
      const timer = setInterval(() => {
        setCurrentTime(prev => {
          if (prev >= duration) {
            nextVideo()
            return 0
          }
          return prev + 1
        })
      }, 1000)
      return () => clearInterval(timer)
    }
  }, [isPlaying, duration, nextVideo])

  return (
    <div className="h-screen bg-black relative overflow-hidden">
      {/* 视频背景 */}
      <div 
        className="absolute inset-0"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onClick={togglePlay}
      >
        {/* 模拟视频内容 */}
        <div className="w-full h-full bg-gradient-to-br from-purple-900 via-blue-800 to-indigo-900 relative">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white">
              <div className="w-40 h-40 bg-white/20 rounded-full flex items-center justify-center mb-6 mx-auto backdrop-blur-sm">
                <span className="text-6xl">
                  {currentIndex === 0 ? '🏗️' : currentIndex === 1 ? '🌌' : '🎓'}
                </span>
              </div>
              <h3 className="text-xl font-bold mb-2 px-4">{currentContent.title}</h3>
              <p className="text-sm opacity-80 px-6">{currentContent.description}</p>
            </div>
          </div>

          {/* 播放暂停指示器 */}
          {!isPlaying && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-20 h-20 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm">
                <div className="w-0 h-0 border-l-[20px] border-l-white border-y-[12px] border-y-transparent ml-1"></div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 顶部状态栏 */}
      <div className="absolute top-0 left-0 right-0 h-12 bg-gradient-to-b from-black/50 to-transparent z-10">
        <div className="flex items-center justify-between px-4 pt-2">
          <div className="text-white text-sm">视频号</div>
          <div className="flex items-center space-x-2">
            <div className="w-1 h-1 bg-white rounded-full"></div>
            <div className="w-1 h-1 bg-white rounded-full"></div>
            <div className="w-1 h-1 bg-white rounded-full"></div>
          </div>
        </div>
      </div>

      {/* 右侧交互按钮 */}
      <div className="absolute right-4 bottom-32 flex flex-col items-center space-y-6 z-20">
        {/* 作者头像 */}
        <div className="relative">
          <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white">
            <Image
              src={currentContent.authorAvatar}
              alt={currentContent.author}
              width={48}
              height={48}
              className="w-full h-full object-cover"
            />
          </div>
          {!isFollowing && (
            <motion.button
              onClick={handleFollow}
              whileTap={{ scale: 0.8 }}
              className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"
            >
              <span className="text-white text-xs font-bold">+</span>
            </motion.button>
          )}
        </div>

        {/* 点赞 */}
        <motion.button
          onClick={handleLike}
          whileTap={{ scale: 0.8 }}
          className="flex flex-col items-center space-y-1"
        >
          <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-all ${
            isLiked ? 'bg-red-500' : 'bg-black/20 backdrop-blur-sm'
          }`}>
            <motion.div
              animate={isLiked ? { scale: [1, 1.2, 1] } : {}}
              transition={{ duration: 0.3 }}
            >
              ❤️
            </motion.div>
          </div>
          <span className="text-white text-xs">
            {formatNumber(currentContent.likes + (isLiked ? 1 : 0))}
          </span>
        </motion.button>

        {/* 评论 */}
        <button 
          onClick={handleComment}
          className="flex flex-col items-center space-y-1"
        >
          <div className="w-12 h-12 rounded-full bg-black/20 backdrop-blur-sm flex items-center justify-center">
            💬
          </div>
          <span className="text-white text-xs">{formatNumber(currentContent.comments)}</span>
        </button>

        {/* 分享 */}
        <button 
          onClick={handleShare}
          className="flex flex-col items-center space-y-1"
        >
          <div className="w-12 h-12 rounded-full bg-black/20 backdrop-blur-sm flex items-center justify-center">
            ↗️
          </div>
          <span className="text-white text-xs">{formatNumber(currentContent.shares)}</span>
        </button>

        {/* 音乐图标 */}
        <div className="flex flex-col items-center space-y-1">
          <motion.div
            animate={{ rotate: isPlaying ? 360 : 0 }}
            transition={{ duration: 3, repeat: isPlaying ? Infinity : 0, ease: "linear" }}
            className="w-12 h-12 rounded-full bg-black/20 backdrop-blur-sm flex items-center justify-center"
          >
            🎵
          </motion.div>
        </div>
      </div>

      {/* 底部信息区域 */}
      <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent z-10">
        {/* 作者信息 */}
        <div className="flex items-center space-x-3 mb-3">
          <span className="text-white font-medium">@{currentContent.author}</span>
          {currentContent.verified && (
            <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs">✓</span>
            </div>
          )}
        </div>

        {/* 视频描述 */}
        <p className="text-white text-sm mb-3 leading-relaxed">
          {currentContent.description}
        </p>

        {/* 标签 */}
        <div className="flex flex-wrap gap-2 mb-3">
          {currentContent.tags.map((tag, index) => (
            <span key={index} className="text-blue-300 text-sm">
              {tag}
            </span>
          ))}
        </div>

        {/* 音乐信息 */}
        <div className="flex items-center space-x-2 mb-4">
          <span className="text-white text-xs">🎵</span>
          <span className="text-white text-xs opacity-80">{currentContent.music}</span>
        </div>

        {/* 进度条 */}
        <div className="w-full h-1 bg-white/20 rounded-full overflow-hidden">
          <div 
            className="h-full bg-white rounded-full transition-all duration-1000"
            style={{ width: `${(currentTime / duration) * 100}%` }}
          />
        </div>
      </div>

      {/* 底部导航栏 */}
      <div className="absolute bottom-0 left-0 right-0 h-20 bg-black/80 backdrop-blur-sm">
        <div className="flex items-center justify-around h-full px-4">
          <div className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 text-white">🏠</div>
            <span className="text-white text-xs">首页</span>
          </div>
          <div className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 text-gray-400">🔍</div>
            <span className="text-gray-400 text-xs">发现</span>
          </div>
          <div className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 text-gray-400">➕</div>
            <span className="text-gray-400 text-xs">发布</span>
          </div>
          <div className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 text-gray-400">💬</div>
            <span className="text-gray-400 text-xs">消息</span>
          </div>
          <div className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 text-gray-400">👤</div>
            <span className="text-gray-400 text-xs">我的</span>
          </div>
        </div>
      </div>

      {/* 评论弹窗 */}
      <AnimatePresence>
        {showComments && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-end"
            onClick={() => setShowComments(false)}
          >
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              className="w-full bg-white rounded-t-3xl max-h-[70vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* 评论头部 */}
              <div className="flex items-center justify-between p-4 border-b border-gray-100">
                <h3 className="text-lg font-semibold text-gray-900">
                  {formatNumber(currentContent.comments)}条评论
                </h3>
                <button
                  onClick={() => setShowComments(false)}
                  className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center"
                >
                  <span className="text-gray-500">✕</span>
                </button>
              </div>

              {/* 评论列表 */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {[
                  { user: 'AI建筑师', avatar: '🏗️', content: '这个智慧城市设计太震撼了！', time: '2分钟前', likes: 128 },
                  { user: '科技爱好者', avatar: '🤖', content: 'NextGen 2025真的是未来趋势', time: '5分钟前', likes: 89 },
                  { user: '城市规划师', avatar: '🏙️', content: '想了解更多技术细节', time: '8分钟前', likes: 56 },
                  { user: '学生小王', avatar: '🎓', content: '学到了很多，感谢分享！', time: '10分钟前', likes: 34 }
                ].map((comment, index) => (
                  <div key={index} className="flex space-x-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center flex-shrink-0">
                      <span className="text-white">{comment.avatar}</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-gray-900">{comment.user}</span>
                        <span className="text-gray-500 text-sm">{comment.time}</span>
                      </div>
                      <p className="text-gray-700 mb-2">{comment.content}</p>
                      <div className="flex items-center space-x-4">
                        <button className="flex items-center space-x-1 text-gray-500 hover:text-red-500">
                          <span className="text-sm">❤️</span>
                          <span className="text-sm">{comment.likes}</span>
                        </button>
                        <button className="text-gray-500 hover:text-blue-500 text-sm">回复</button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 评论输入 */}
              <div className="p-4 border-t border-gray-100 bg-gray-50">
                <div className="flex space-x-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm">我</span>
                  </div>
                  <div className="flex-1 flex space-x-2">
                    <input
                      type="text"
                      placeholder="说点什么..."
                      className="flex-1 bg-white border border-gray-200 rounded-full px-4 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-blue-500"
                    />
                    <button className="bg-blue-500 hover:bg-blue-600 rounded-full px-6 py-2 text-white font-medium">
                      发送
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 分享弹窗 */}
      <AnimatePresence>
        {showShare && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-end"
            onClick={() => setShowShare(false)}
          >
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              className="w-full bg-white rounded-t-3xl p-6"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">分享到</h3>
                <button
                  onClick={() => setShowShare(false)}
                  className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center"
                >
                  <span className="text-gray-500">✕</span>
                </button>
              </div>

              <div className="grid grid-cols-4 gap-6 mb-8">
                {[
                  { name: '微信好友', icon: '💬', color: 'bg-green-500' },
                  { name: '朋友圈', icon: '🌟', color: 'bg-green-600' },
                  { name: 'QQ好友', icon: '🐧', color: 'bg-blue-500' },
                  { name: 'QQ空间', icon: '⭐', color: 'bg-yellow-500' },
                  { name: '微博', icon: '📱', color: 'bg-red-500' },
                  { name: '抖音', icon: '🎵', color: 'bg-black' },
                  { name: '复制链接', icon: '🔗', color: 'bg-gray-600' },
                  { name: '保存视频', icon: '📥', color: 'bg-purple-500' }
                ].map((platform, index) => (
                  <button
                    key={index}
                    className="flex flex-col items-center space-y-2"
                  >
                    <div className={`w-14 h-14 ${platform.color} rounded-2xl flex items-center justify-center`}>
                      <span className="text-white text-xl">{platform.icon}</span>
                    </div>
                    <span className="text-gray-700 text-sm text-center">{platform.name}</span>
                  </button>
                ))}
              </div>

              <button
                onClick={() => setShowShare(false)}
                className="w-full py-4 bg-gray-100 hover:bg-gray-200 rounded-2xl text-gray-700 font-medium"
              >
                取消
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
