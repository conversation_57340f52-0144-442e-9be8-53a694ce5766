'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

export default function WeChatVideoHomepage() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLiked, setIsLiked] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [showComments, setShowComments] = useState(false)
  const [showShare, setShowShare] = useState(false)
  const [isPlaying, setIsPlaying] = useState(true)
  const [activeTab, setActiveTab] = useState('推荐')
  const videoRef = useRef<HTMLVideoElement>(null)

  // 视频内容数据
  const videoContent = [
    {
      id: 1,
      title: "震撼！全球最高摩天大楼建设纪录片",
      author: "建筑奇迹",
      authorAvatar: "🏢",
      verified: true,
      description: "🏗️ 见证828米迪拜塔的建设全过程，工程技术的巅峰之作！",
      videoUrl: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
      likes: 1200000,
      comments: 89000,
      shares: 156000,
      videoTime: "12:30",
      friendCount: "1个朋友关注"
    },
    {
      id: 2,
      title: "AI机器人自动化建房，24小时完工！",
      author: "未来建筑师",
      authorAvatar: "🤖",
      verified: true,
      description: "🚀 革命性的3D打印建筑技术，机器人24小时自动建房全过程！",
      videoUrl: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
      likes: 890000,
      comments: 67000,
      shares: 123000,
      videoTime: "08:45",
      friendCount: "5个朋友关注"
    },
    {
      id: 3,
      title: "我家装修日记30天，终于看到希望了！",
      author: "装修小白",
      authorAvatar: "🏠",
      verified: false,
      description: "💪 历时一个月的装修终于有起色了，分享一下踩过的坑！",
      videoUrl: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
      likes: 456000,
      comments: 23000,
      shares: 78000,
      videoTime: "06:30",
      friendCount: "5个朋友关注"
    }
  ]

  const currentContent = videoContent[currentIndex]

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    }
    return num.toString()
  }

  // 交互处理函数
  const handleLike = useCallback(() => setIsLiked(!isLiked), [isLiked])
  const handleComment = useCallback(() => setShowComments(true), [])
  const handleShare = useCallback(() => setShowShare(true), [])
  const togglePlay = useCallback(() => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }, [isPlaying])

  // 视频播放控制
  useEffect(() => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.play()
      } else {
        videoRef.current.pause()
      }
    }
  }, [currentIndex, isPlaying])

  // 标签切换处理函数
  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab)
    setCurrentIndex(0)
    setIsLiked(false)
    setIsFollowing(false)
    setIsPlaying(true)
  }, [])

  // 视频切换函数
  const nextVideo = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % videoContent.length)
    setIsLiked(false)
    setIsFollowing(false)
    setIsPlaying(true)
  }, [videoContent.length])

  const prevVideo = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + videoContent.length) % videoContent.length)
    setIsLiked(false)
    setIsFollowing(false)
    setIsPlaying(true)
  }, [videoContent.length])

  // 触摸手势处理
  const [touchStart, setTouchStart] = useState(0)
  const [touchEnd, setTouchEnd] = useState(0)

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientY)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientY)
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    const distance = touchStart - touchEnd
    const isUpSwipe = distance > 50
    const isDownSwipe = distance < -50

    if (isUpSwipe) {
      nextVideo()
    }
    if (isDownSwipe) {
      prevVideo()
    }
  }

  return (
    <div className="h-screen bg-gray-800 relative overflow-hidden">
      {/* 全屏视频内容区 - 带滑动动画 */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="absolute inset-0 bg-gray-800"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onClick={togglePlay}
        >
          {/* 视频播放器 */}
          <div className="w-full h-full relative">
            <video
              ref={videoRef}
              src={currentContent.videoUrl}
              className="w-full h-full object-cover"
              loop
              muted
              playsInline
              autoPlay
              onLoadedData={() => {
                if (videoRef.current && isPlaying) {
                  videoRef.current.play()
                }
              }}
            />

            {/* 视频信息覆盖层 */}
            <div className="absolute bottom-32 left-0 right-0 px-4">
              <div className="bg-black/40 backdrop-blur-sm rounded-lg p-4">
                <h3 className="text-white text-lg font-bold mb-2">{currentContent.title}</h3>
                <p className="text-white/90 text-sm">{currentContent.description}</p>
              </div>
            </div>
          </div>

          {/* 播放暂停指示器 */}
          {!isPlaying && (
            <div className="absolute inset-0 flex items-center justify-center z-20">
              <div className="w-20 h-20 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm">
                <div className="w-0 h-0 border-l-[20px] border-l-white border-y-[12px] border-y-transparent ml-1"></div>
              </div>
            </div>
          )}
        </motion.div>
      </AnimatePresence>

      {/* 顶部导航栏 - 固定 */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-gray-800">
        <div className="flex items-center justify-center px-4 pt-12 pb-4">
          <div className="flex items-center space-x-8">
            <button
              onClick={() => handleTabChange('推荐')}
              className={`text-lg font-medium transition-all duration-200 relative ${
                activeTab === '推荐' ? 'text-white' : 'text-white/70'
              }`}
            >
              推荐
              {activeTab === '推荐' && (
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full"></div>
              )}
            </button>
            <button
              onClick={() => handleTabChange('关注')}
              className={`text-lg font-medium transition-all duration-200 relative ${
                activeTab === '关注' ? 'text-white' : 'text-white/70'
              }`}
            >
              关注
              {activeTab === '关注' && (
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full"></div>
              )}
            </button>
            <button
              onClick={() => handleTabChange('朋友')}
              className={`text-lg font-medium transition-all duration-200 flex items-center space-x-1 relative ${
                activeTab === '朋友' ? 'text-white' : 'text-white/70'
              }`}
            >
              <span>朋友</span>
              <span className="text-red-500 text-sm">♥</span>
              {activeTab === '朋友' && (
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full"></div>
              )}
            </button>
          </div>

          {/* 右上角功能按钮 */}
          <div className="absolute right-4 top-12 flex items-center space-x-3">
            <button className="w-8 h-8 flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
            <button className="w-8 h-8 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full"></div>
              <div className="w-1 h-1 bg-white rounded-full mx-0.5"></div>
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </button>
          </div>
        </div>
      </div>

      {/* 视频时间显示 */}
      <div className="fixed top-16 right-4 bg-black/60 backdrop-blur-sm rounded-lg px-2 py-1 z-40">
        <span className="text-white text-sm font-medium">{currentContent.videoTime}</span>
      </div>

      {/* 底部功能区域 */}
      <div className="fixed bottom-20 left-0 right-0 z-40">
        <div className="bg-gray-800 px-4 py-3">
          <div className="flex items-center justify-between">
            {/* 左侧用户信息 */}
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 rounded-full overflow-hidden">
                <div className="w-full h-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                  <span className="text-white text-lg">{currentContent.authorAvatar}</span>
                </div>
              </div>
              
              <div className="flex flex-col">
                <div className="flex items-center space-x-1">
                  <span className="text-white font-medium text-base">{currentContent.author}</span>
                  {currentContent.verified && (
                    <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  )}
                </div>
                <span className="text-gray-300 text-sm">{currentContent.friendCount}</span>
              </div>
            </div>

            {/* 右侧交互按钮 */}
            <div className="flex items-center space-x-6">
              <button onClick={handleShare} className="flex flex-col items-center">
                <div className="w-8 h-8 flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
                  </svg>
                </div>
                <span className="text-white text-xs">{formatNumber(currentContent.shares)}</span>
              </button>

              <motion.button onClick={handleLike} whileTap={{ scale: 0.8 }} className="flex flex-col items-center">
                <div className="w-8 h-8 flex items-center justify-center">
                  <motion.div animate={isLiked ? { scale: [1, 1.2, 1] } : {}} transition={{ duration: 0.3 }}>
                    {isLiked ? (
                      <svg className="w-6 h-6 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                      </svg>
                    ) : (
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                      </svg>
                    )}
                  </motion.div>
                </div>
                <span className="text-white text-xs">{formatNumber(currentContent.likes + (isLiked ? 1 : 0))}</span>
              </motion.button>

              <button onClick={handleComment} className="flex flex-col items-center">
                <div className="w-8 h-8 flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                  </svg>
                </div>
                <span className="text-white text-xs">{formatNumber(currentContent.comments)}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
