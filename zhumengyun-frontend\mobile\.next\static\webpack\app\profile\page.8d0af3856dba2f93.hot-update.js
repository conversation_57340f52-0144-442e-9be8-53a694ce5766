"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./src/app/profile/page.tsx":
/*!**********************************!*\
  !*** ./src/app/profile/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptimizedProfilePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_CreatorDashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CreatorDashboard */ \"(app-pages-browser)/./src/components/CreatorDashboard.tsx\");\n/* harmony import */ var _components_PointsManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/PointsManager */ \"(app-pages-browser)/./src/components/PointsManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction OptimizedProfilePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('projects');\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 客户端状态设置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptimizedProfilePage.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"OptimizedProfilePage.useEffect\"], []);\n    // NextGen 2025 用户资料数据\n    const userProfile = {\n        id: 'user-1',\n        name: 'NextGen创作者',\n        avatar: '🚀',\n        title: 'AI原生数字生活先锋',\n        bio: '探索NextGen 2025数字生活的先锋用户，专注于AI辅助创作、Web3创新和元宇宙体验。跨越七大生态系统的全能创作者。',\n        location: '数字元宇宙',\n        verified: true,\n        stats: {\n            projects: 156,\n            followers: 15600,\n            following: 890,\n            likes: 234000\n        },\n        skills: [\n            'AI创作',\n            'Web3开发',\n            'NFT设计',\n            'VR/AR',\n            '元宇宙建设',\n            '智能合约',\n            'BIM+AI',\n            '区块链'\n        ],\n        achievements: [\n            {\n                id: 'ach-1',\n                name: 'AI创作先锋',\n                icon: '🧠',\n                description: '使用AI工具创作100个作品',\n                unlockedAt: '2025-01-15'\n            },\n            {\n                id: 'ach-2',\n                name: 'Web3创新者',\n                icon: '💎',\n                description: '发布50个NFT作品',\n                unlockedAt: '2025-01-10'\n            },\n            {\n                id: 'ach-3',\n                name: '元宇宙建设者',\n                icon: '🌌',\n                description: '创建10个虚拟空间',\n                unlockedAt: '2025-01-05'\n            },\n            {\n                id: 'ach-4',\n                name: '生态系统大师',\n                icon: '🚀',\n                description: '在所有7个生态系统中达到专家级',\n                unlockedAt: '2025-01-01'\n            }\n        ],\n        // Web3 DID 身份信息\n        did: {\n            address: '0x742d35Cc6634C0532925a3b8D4C0532925a3b8D4',\n            reputation: 950,\n            nftCount: 156,\n            tokenBalance: 25680,\n            verifications: {\n                biometric: true,\n                social: true,\n                professional: true,\n                multiSig: true // 多重签名\n            },\n            credentials: [\n                {\n                    id: 'cred-1',\n                    type: 'education',\n                    title: '计算机科学硕士',\n                    issuer: '清华大学',\n                    verifiedAt: '2024-06-15',\n                    onChain: true\n                },\n                {\n                    id: 'cred-2',\n                    type: 'skill',\n                    title: 'AI专家认证',\n                    issuer: 'NextGen AI学院',\n                    verifiedAt: '2024-12-01',\n                    onChain: true\n                },\n                {\n                    id: 'cred-3',\n                    type: 'work',\n                    title: '高级区块链开发工程师',\n                    issuer: 'Web3创新实验室',\n                    verifiedAt: '2024-08-20',\n                    onChain: true\n                },\n                {\n                    id: 'cred-4',\n                    type: 'certification',\n                    title: 'NFT创作者认证',\n                    issuer: 'OpenSea认证中心',\n                    verifiedAt: '2024-11-10',\n                    onChain: true\n                }\n            ]\n        }\n    };\n    // NextGen 2025 用户项目数据\n    const userProjects = [\n        {\n            id: 'proj-1',\n            title: 'AI原生智慧城市综合体',\n            description: 'GPT-5驱动的智慧城市设计，集成VR漫游、区块链管理和IoT控制',\n            thumbnail: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=300&h=200&fit=crop',\n            likes: 8250,\n            views: 45420,\n            createdAt: '2025-01-15'\n        },\n        {\n            id: 'proj-2',\n            title: '元宇宙社交空间NFT',\n            description: 'WebXR驱动的沉浸式社交元宇宙，支持虚拟身份和NFT商品交易',\n            thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop',\n            likes: 12890,\n            views: 78210,\n            createdAt: '2025-01-10'\n        },\n        {\n            id: 'proj-3',\n            title: 'AI驱动VR学习实验室',\n            description: 'GPT-5个性化VR学习空间，支持物理化学生物实验和区块链认证',\n            thumbnail: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop',\n            likes: 18100,\n            views: 125750,\n            createdAt: '2025-01-05'\n        },\n        {\n            id: 'proj-4',\n            title: 'IoT智能家居生态系统',\n            description: 'AI原生智能家居管理，支持语音手势控制、设备互联和健康监测',\n            thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop',\n            likes: 9500,\n            views: 56400,\n            createdAt: '2025-01-01'\n        }\n    ];\n    // 格式化数字\n    const formatNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptimizedProfilePage.useCallback[formatNumber]\": (num)=>{\n            if (num >= 1000000) {\n                return (num / 1000000).toFixed(1) + 'M';\n            }\n            if (num >= 1000) {\n                return (num / 1000).toFixed(1) + 'k';\n            }\n            return num.toString();\n        }\n    }[\"OptimizedProfilePage.useCallback[formatNumber]\"], []);\n    // 格式化日期\n    const formatDate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptimizedProfilePage.useCallback[formatDate]\": (dateString)=>{\n            const date = new Date(dateString);\n            return date.toLocaleDateString('zh-CN', {\n                year: 'numeric',\n                month: 'short',\n                day: 'numeric'\n            });\n        }\n    }[\"OptimizedProfilePage.useCallback[formatDate]\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin w-8 h-8 border-2 border-purple-400 border-t-transparent rounded-full\"\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 overflow-hidden pointer-events-none\",\n                children: Array.from({\n                    length: 30\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        className: \"absolute w-1 h-1 bg-white rounded-full\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\")\n                        },\n                        animate: {\n                            opacity: [\n                                0.2,\n                                1,\n                                0.2\n                            ],\n                            scale: [\n                                0.5,\n                                1.5,\n                                0.5\n                            ]\n                        },\n                        transition: {\n                            duration: 3 + Math.random() * 2,\n                            repeat: Infinity,\n                            delay: Math.random() * 2\n                        }\n                    }, i, false, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sticky top-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.back(),\n                                className: \"p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 19l-7-7 7-7\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg font-bold\",\n                                children: \"个人资料\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"bg-white/10 backdrop-blur-sm rounded-3xl p-6 border border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-3xl\",\n                                                children: userProfile.avatar\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            userProfile.verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-white\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold mb-1\",\n                                                children: userProfile.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-300 text-sm mb-1\",\n                                                children: userProfile.title\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-200 text-xs flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3 mr-1\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    userProfile.location\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-purple-100 text-sm mb-6 leading-relaxed\",\n                                children: userProfile.bio\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-4 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: userProfile.stats.projects\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-300\",\n                                                children: \"项目\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: formatNumber(userProfile.stats.followers)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-300\",\n                                                children: \"关注者\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: userProfile.stats.following\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-300\",\n                                                children: \"关注中\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: formatNumber(userProfile.stats.likes)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-300\",\n                                                children: \"获赞\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-purple-300 mb-3\",\n                                        children: \"专业技能\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: userProfile.skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 bg-purple-500/30 text-purple-200 rounded-full text-xs border border-purple-400/30\",\n                                                children: skill\n                                            }, index, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex-1 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-medium hover:shadow-lg transition-all\",\n                                        children: \"编辑资料\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex-1 py-3 bg-white/10 text-white rounded-xl font-medium hover:bg-white/20 transition-colors border border-white/20\",\n                                        children: \"分享资料\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push('/verification'),\n                                        className: \"flex items-center space-x-3 p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-500/30 hover:bg-green-500/30 transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: \"实名认证\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-200\",\n                                                        children: \"身份验证\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push('/withdraw'),\n                                        className: \"flex items-center space-x-3 p-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl border border-yellow-500/30 hover:bg-yellow-500/30 transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xl\",\n                                                children: \"\\uD83D\\uDCB0\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: \"收益提现\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-yellow-200\",\n                                                        children: \"合规提现\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4 mt-8 mb-6 overflow-x-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('projects'),\n                                className: \"text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap \".concat(activeTab === 'projects' ? 'text-purple-400 border-purple-400' : 'text-purple-200 border-transparent hover:text-purple-300'),\n                                children: \"我的项目\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('achievements'),\n                                className: \"text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap \".concat(activeTab === 'achievements' ? 'text-purple-400 border-purple-400' : 'text-purple-200 border-transparent hover:text-purple-300'),\n                                children: \"成就徽章\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('stats'),\n                                className: \"text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap \".concat(activeTab === 'stats' ? 'text-purple-400 border-purple-400' : 'text-purple-200 border-transparent hover:text-purple-300'),\n                                children: \"数据统计\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('did'),\n                                className: \"text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap \".concat(activeTab === 'did' ? 'text-purple-400 border-purple-400' : 'text-purple-200 border-transparent hover:text-purple-300'),\n                                children: \"\\uD83C\\uDD94 DID身份\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('web3'),\n                                className: \"text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap \".concat(activeTab === 'web3' ? 'text-purple-400 border-purple-400' : 'text-purple-200 border-transparent hover:text-purple-300'),\n                                children: \"\\uD83D\\uDC8E 数字资产\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('creator'),\n                                className: \"text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap \".concat(activeTab === 'creator' ? 'text-purple-400 border-purple-400' : 'text-purple-200 border-transparent hover:text-purple-300'),\n                                children: \"\\uD83C\\uDFA8 创作者\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('points'),\n                                className: \"text-sm font-medium pb-2 border-b-2 transition-colors whitespace-nowrap \".concat(activeTab === 'points' ? 'text-purple-400 border-purple-400' : 'text-purple-200 border-transparent hover:text-purple-300'),\n                                children: \"\\uD83C\\uDF1F 积分\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        mode: \"wait\",\n                        children: [\n                            activeTab === 'projects' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                className: \"space-y-4\",\n                                children: userProjects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/20 hover:border-white/30 transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-500 flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        src: project.thumbnail,\n                                                        alt: project.title,\n                                                        width: 96,\n                                                        height: 96,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-white mb-1\",\n                                                            children: project.title\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-200 text-sm mb-2 line-clamp-2\",\n                                                            children: project.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-6 h-6 bg-white/10 rounded-full flex items-center justify-center text-xs\",\n                                                                                    children: \"\\uD83D\\uDC4D\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: formatNumber(project.likes)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                    lineNumber: 510,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-6 h-6 bg-white/10 rounded-full flex items-center justify-center text-xs\",\n                                                                                    children: \"\\uD83D\\uDD04\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"33\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                    lineNumber: 517,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-6 h-6 bg-white/10 rounded-full flex items-center justify-center text-xs\",\n                                                                                    children: \"❤️\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                    lineNumber: 521,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"45\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                    lineNumber: 524,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 520,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"flex flex-col items-center space-y-1 text-gray-400 hover:text-white transition-colors\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-6 h-6 bg-white/10 rounded-full flex items-center justify-center text-xs\",\n                                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                    lineNumber: 528,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"67\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                    lineNumber: 531,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 527,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-purple-300\",\n                                                                    children: formatDate(project.createdAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, project.id, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 17\n                                    }, this))\n                            }, \"projects\", false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'achievements' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: userProfile.achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl mb-2\",\n                                                children: achievement.icon\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-white mb-1\",\n                                                children: achievement.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-200 text-xs mb-2\",\n                                                children: achievement.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-300 text-xs\",\n                                                children: formatDate(achievement.unlockedAt)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, achievement.id, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 17\n                                    }, this))\n                            }, \"achievements\", false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'stats' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-white mb-4\",\n                                            children: \"活跃度统计\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-purple-200\",\n                                                            children: \"本月发布项目\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"8 个\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-purple-200\",\n                                                            children: \"本月获得点赞\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"342 个\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-purple-200\",\n                                                            children: \"本月新增关注者\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"89 人\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-purple-200\",\n                                                            children: \"平均项目评分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"4.8 ⭐\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, this)\n                            }, \"stats\", false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'did' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83C\\uDD94\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"去中心化身份 (DID)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-3 py-1 bg-green-500 rounded-full text-xs font-medium\",\n                                                                children: \"已验证\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-yellow-400 text-sm\",\n                                                                children: [\n                                                                    \"信誉: \",\n                                                                    userProfile.did.reputation,\n                                                                    \"/1000\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-400\",\n                                                                children: userProfile.did.nftCount\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-purple-200\",\n                                                                children: \"NFT资产\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-400\",\n                                                                children: userProfile.did.tokenBalance.toLocaleString()\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-green-200\",\n                                                                children: \"NGT代币\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/5 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-purple-200 mb-2\",\n                                                        children: \"DID地址:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                className: \"text-sm text-white font-mono\",\n                                                                children: userProfile.did.address\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-purple-400 hover:text-purple-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"\\uD83D\\uDCCB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"身份验证状态\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg border \".concat(userProfile.did.verifications.biometric ? 'bg-green-500/20 border-green-500/30' : 'bg-red-500/20 border-red-500/30'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg\",\n                                                                        children: \"\\uD83D\\uDC46\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 651,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"生物识别\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 652,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-300\",\n                                                                children: \"指纹、面部识别验证\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(userProfile.did.verifications.biometric ? 'bg-green-500 text-white' : 'bg-red-500 text-white'),\n                                                                    children: userProfile.did.verifications.biometric ? '已验证' : '未验证'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg border \".concat(userProfile.did.verifications.social ? 'bg-green-500/20 border-green-500/30' : 'bg-red-500/20 border-red-500/30'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg\",\n                                                                        children: \"\\uD83D\\uDC65\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 664,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"社交验证\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 665,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-300\",\n                                                                children: \"社交关系网络验证\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(userProfile.did.verifications.social ? 'bg-green-500 text-white' : 'bg-red-500 text-white'),\n                                                                    children: userProfile.did.verifications.social ? '已验证' : '未验证'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg border \".concat(userProfile.did.verifications.professional ? 'bg-green-500/20 border-green-500/30' : 'bg-red-500/20 border-red-500/30'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg\",\n                                                                        children: \"\\uD83C\\uDF93\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 677,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"专业认证\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 678,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-300\",\n                                                                children: \"学历、技能认证\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(userProfile.did.verifications.professional ? 'bg-green-500 text-white' : 'bg-red-500 text-white'),\n                                                                    children: userProfile.did.verifications.professional ? '已验证' : '未验证'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg border \".concat(userProfile.did.verifications.multiSig ? 'bg-green-500/20 border-green-500/30' : 'bg-red-500/20 border-red-500/30'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg\",\n                                                                        children: \"\\uD83D\\uDD10\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"多重签名\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-300\",\n                                                                children: \"多设备安全验证\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(userProfile.did.verifications.multiSig ? 'bg-green-500 text-white' : 'bg-red-500 text-white'),\n                                                                    children: userProfile.did.verifications.multiSig ? '已验证' : '未验证'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"链上凭证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: userProfile.did.credentials.map((credential)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: credential.type === 'education' ? '🎓' : credential.type === 'skill' ? '🛠️' : credential.type === 'work' ? '💼' : '📜'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 711,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-white\",\n                                                                                children: credential.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 716,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            credential.onChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 bg-blue-500 rounded-full text-xs\",\n                                                                                children: \"链上验证\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 720,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-green-400 text-sm\",\n                                                                                children: \"✓\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 722,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 718,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            \"颁发机构: \",\n                                                                            credential.issuer\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            \"验证时间: \",\n                                                                            credential.verifiedAt\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 727,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, credential.id, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, \"did\", true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'web3' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"\\uD83D\\uDCB0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"代币资产\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-lg p-4 border border-yellow-500/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-2xl\",\n                                                                        children: \"\\uD83E\\uDE99\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-yellow-400\",\n                                                                        children: \"NGT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: userProfile.did.tokenBalance.toLocaleString()\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-yellow-300\",\n                                                                children: \"平台治理代币\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 758,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: \"≈ $12,840 USD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg p-4 border border-purple-500/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-2xl\",\n                                                                        children: \"\\uD83C\\uDFA8\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 764,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-purple-400\",\n                                                                        children: \"CRT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 765,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: \"8,560\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-purple-300\",\n                                                                children: \"创作者代币\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: \"≈ $4,280 USD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-4 border border-blue-500/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-2xl\",\n                                                                        children: \"⚡\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 774,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-blue-400\",\n                                                                        children: \"SKL\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 775,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: \"15,240\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 777,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-blue-300\",\n                                                                children: \"技能代币\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: \"≈ $7,620 USD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-green-500/20 to-teal-500/20 rounded-lg p-4 border border-green-500/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-2xl\",\n                                                                        children: \"\\uD83D\\uDC65\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 784,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-green-400\",\n                                                                        children: \"FAN\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: \"3,890\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-green-300\",\n                                                                children: \"粉丝代币\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: \"≈ $1,945 USD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDDBC️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"NFT收藏\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400 font-medium\",\n                                                        children: [\n                                                            userProfile.did.nftCount,\n                                                            \" 个\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-3 gap-3\",\n                                                children: [\n                                                    {\n                                                        id: 1,\n                                                        name: 'AI艺术 #001',\n                                                        rarity: 'legendary',\n                                                        price: '2.5 ETH'\n                                                    },\n                                                    {\n                                                        id: 2,\n                                                        name: '建筑设计 #042',\n                                                        rarity: 'rare',\n                                                        price: '1.2 ETH'\n                                                    },\n                                                    {\n                                                        id: 3,\n                                                        name: '元宇宙空间 #018',\n                                                        rarity: 'epic',\n                                                        price: '3.8 ETH'\n                                                    },\n                                                    {\n                                                        id: 4,\n                                                        name: 'VR体验 #007',\n                                                        rarity: 'rare',\n                                                        price: '0.9 ETH'\n                                                    },\n                                                    {\n                                                        id: 5,\n                                                        name: '智能合约 #156',\n                                                        rarity: 'common',\n                                                        price: '0.3 ETH'\n                                                    },\n                                                    {\n                                                        id: 6,\n                                                        name: 'DeFi协议 #089',\n                                                        rarity: 'epic',\n                                                        price: '2.1 ETH'\n                                                    }\n                                                ].map((nft)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-3 hover:bg-white/10 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg mb-2 flex items-center justify-center text-2xl\",\n                                                                children: \"\\uD83C\\uDFA8\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 813,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white truncate\",\n                                                                children: nft.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 816,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(nft.rarity === 'legendary' ? 'bg-yellow-500 text-black' : nft.rarity === 'epic' ? 'bg-purple-500 text-white' : nft.rarity === 'rare' ? 'bg-blue-500 text-white' : 'bg-gray-500 text-white'),\n                                                                    children: nft.rarity === 'legendary' ? '传说' : nft.rarity === 'epic' ? '史诗' : nft.rarity === 'rare' ? '稀有' : '普通'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 818,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-green-400 mt-1\",\n                                                                children: nft.price\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, nft.id, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 812,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 803,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"\\uD83D\\uDCC8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 838,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"DeFi投资\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: \"\\uD83C\\uDFE6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 845,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-white\",\n                                                                                children: \"流动性挖矿\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 844,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-400 font-medium\",\n                                                                        children: \"+12.5% APY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 848,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: \"投入: 5,000 NGT + 2.5 ETH\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 851,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: \"收益: 625 NGT (本月)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 842,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: \"\\uD83D\\uDCB0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 859,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-white\",\n                                                                                children: \"创作者贷款\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 860,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 858,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 font-medium\",\n                                                                        children: \"8.5% APR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 862,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: \"借款: 10,000 USDC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 865,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: \"抵押: 15 个NFT作品\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 866,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: \"\\uD83D\\uDEE1️\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 873,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-white\",\n                                                                                children: \"收益保险\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 874,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 872,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-purple-400 font-medium\",\n                                                                        children: \"已投保\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 876,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: \"保额: 50,000 USDC\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 879,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: \"保费: 250 USDC/年\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 880,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"\\uD83C\\uDFDB️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"DAO治理\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 890,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 888,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-400\",\n                                                                children: \"15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-purple-200\",\n                                                                children: \"参与提案\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 895,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-400\",\n                                                                children: \"89%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 898,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-green-200\",\n                                                                children: \"投票参与率\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-white\",\n                                                                        children: \"提案 #042: 平台手续费调整\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 905,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 bg-green-500 rounded-full text-xs\",\n                                                                        children: \"已通过\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 906,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"您的投票: 赞成 (1,250 NGT)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 908,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 903,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-white\",\n                                                                        children: \"提案 #043: 新增VR功能\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 912,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 bg-yellow-500 rounded-full text-xs\",\n                                                                        children: \"投票中\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 913,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"截止时间: 2025-01-20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 915,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 910,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 902,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 887,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, \"web3\", true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 738,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'creator' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CreatorDashboard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    userId: userProfile.id\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, this)\n                            }, \"creator\", false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'points' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PointsManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    userId: userProfile.id,\n                                    onPointsUpdate: (points)=>{\n                                        console.log('积分更新:', points);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 942,\n                                    columnNumber: 15\n                                }, this)\n                            }, \"points\", false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 936,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\profile\\\\page.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, this);\n}\n_s(OptimizedProfilePage, \"+13VkjaL+aY2doJgvtiW0n2jPEI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = OptimizedProfilePage;\nvar _c;\n$RefreshReg$(_c, \"OptimizedProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/profile/page.tsx\n"));

/***/ })

});