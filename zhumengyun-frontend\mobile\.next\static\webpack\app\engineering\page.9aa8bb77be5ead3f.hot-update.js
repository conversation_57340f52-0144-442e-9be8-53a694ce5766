/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/engineering/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C2025%5C%5C20250710%5C%5Czhumengyun-frontend%5C%5Cmobile%5C%5Csrc%5C%5Capp%5C%5Cengineering%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C2025%5C%5C20250710%5C%5Czhumengyun-frontend%5C%5Cmobile%5C%5Csrc%5C%5Capp%5C%5Cengineering%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/engineering/page.tsx */ \"(app-pages-browser)/./src/app/engineering/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1QzIwMjUlNUMlNUMyMDI1MDcxMCU1QyU1Q3podW1lbmd5dW4tZnJvbnRlbmQlNUMlNUNtb2JpbGUlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNlbmdpbmVlcmluZyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQXFIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFwyMDI1XFxcXDIwMjUwNzEwXFxcXHpodW1lbmd5dW4tZnJvbnRlbmRcXFxcbW9iaWxlXFxcXHNyY1xcXFxhcHBcXFxcZW5naW5lZXJpbmdcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C2025%5C%5C20250710%5C%5Czhumengyun-frontend%5C%5Cmobile%5C%5Csrc%5C%5Capp%5C%5Cengineering%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkU6XFwyMDI1XFwyMDI1MDcxMFxcemh1bWVuZ3l1bi1mcm9udGVuZFxcbW9iaWxlXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/engineering/page.tsx":
/*!**************************************!*\
  !*** ./src/app/engineering/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DiscoveryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DiscoveryPage() {\n    var _selectedContent_tags;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('video');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // 交互状态\n    const [likedItems, setLikedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [savedItems, setSavedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [followedUsers, setFollowedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [joinedGroups, setJoinedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'c1'\n    ]));\n    // 弹窗状态\n    const [showContentDetail, setShowContentDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showApplyProject, setShowApplyProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNFTModal, setShowNFTModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTipModal, setShowTipModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccessToast, setShowSuccessToast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toastMessage, setToastMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 选中的项目\n    const [selectedContent, setSelectedContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 加载状态\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 筛选后的内容\n    const [filteredContent, setFilteredContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 模拟发布的内容数据（对应发布页面的四大平台）\n    const discoveryContent = {\n        video: [\n            {\n                id: 'v1',\n                title: 'NextGen 2025智慧城市建设项目展示',\n                creator: '工程师·张三',\n                avatar: '👨‍💻',\n                thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',\n                duration: '3:45',\n                views: 12500,\n                likes: 890,\n                publishTime: '2小时前',\n                tags: [\n                    '智慧城市',\n                    'AI技术',\n                    '建筑设计'\n                ],\n                description: '展示了最新的AI驱动智慧城市建设项目，包括智能交通系统和物联网基础设施...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 120\n            },\n            {\n                id: 'v2',\n                title: 'AI辅助建筑设计全流程演示',\n                creator: '建筑师·王设计',\n                avatar: '🏗️',\n                thumbnail: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop',\n                duration: '5:20',\n                views: 8900,\n                likes: 567,\n                publishTime: '4小时前',\n                tags: [\n                    '建筑设计',\n                    'AI辅助',\n                    'BIM'\n                ],\n                description: '完整展示AI辅助建筑设计的全流程，从概念设计到施工图生成...',\n                nftEnabled: false,\n                didVerified: true,\n                rewards: 85\n            },\n            {\n                id: 'v3',\n                title: '元宇宙虚拟展厅设计案例',\n                creator: 'VR设计师·小李',\n                avatar: '🌌',\n                thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',\n                duration: '4:15',\n                views: 15600,\n                likes: 1234,\n                publishTime: '6小时前',\n                tags: [\n                    '元宇宙',\n                    'VR设计',\n                    '虚拟展厅'\n                ],\n                description: '创新的元宇宙虚拟展厅设计，支持多人实时交互和3D展示...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 150\n            }\n        ],\n        discovery: [\n            {\n                id: 'd1',\n                title: '2025年建筑行业AI应用趋势报告',\n                creator: '行业分析师·陈专家',\n                avatar: '📊',\n                images: [\n                    'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',\n                    'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop'\n                ],\n                readTime: '8分钟',\n                views: 5600,\n                likes: 234,\n                publishTime: '1小时前',\n                tags: [\n                    '行业报告',\n                    'AI应用',\n                    '建筑趋势'\n                ],\n                description: '深度分析2025年建筑行业AI应用的最新趋势，包括设计自动化、施工机器人等...',\n                nftEnabled: false,\n                didVerified: true,\n                rewards: 75\n            },\n            {\n                id: 'd2',\n                title: '智能建筑物联网系统设计指南',\n                creator: '物联网工程师·刘技术',\n                avatar: '🔗',\n                images: [\n                    'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop'\n                ],\n                readTime: '12分钟',\n                views: 3400,\n                likes: 189,\n                publishTime: '3小时前',\n                tags: [\n                    '物联网',\n                    '智能建筑',\n                    '系统设计'\n                ],\n                description: '详细介绍智能建筑物联网系统的设计原理、技术架构和实施方案...',\n                nftEnabled: true,\n                didVerified: true,\n                rewards: 95\n            }\n        ],\n        community: [\n            {\n                id: 'c1',\n                title: '建筑师AI工具使用经验分享',\n                creator: '建筑师联盟',\n                avatar: '🏗️',\n                groupType: '技术讨论',\n                members: 2340,\n                posts: 156,\n                publishTime: '30分钟前',\n                tags: [\n                    '经验分享',\n                    'AI工具',\n                    '建筑师'\n                ],\n                description: '分享各种AI工具在建筑设计中的实际应用经验，包括Midjourney、Stable Diffusion等...',\n                isJoined: true,\n                activity: 'high'\n            },\n            {\n                id: 'c2',\n                title: 'Web3建设者技术讨论群',\n                creator: 'Web3建设者',\n                avatar: '🌐',\n                groupType: '技术交流',\n                members: 1567,\n                posts: 89,\n                publishTime: '1小时前',\n                tags: [\n                    'Web3',\n                    '区块链',\n                    '技术讨论'\n                ],\n                description: '讨论Web3技术在建筑和工程领域的应用，包括DeFi、NFT、DAO等...',\n                isJoined: false,\n                activity: 'medium'\n            }\n        ],\n        ecosystem: [\n            {\n                id: 'e1',\n                title: '寻求AI建筑设计合作伙伴',\n                creator: '建筑事务所·王总',\n                avatar: '🏢',\n                budget: '50-100万',\n                duration: '3-6个月',\n                location: '北京',\n                publishTime: '2小时前',\n                tags: [\n                    '项目合作',\n                    'AI建筑',\n                    '设计服务'\n                ],\n                description: '我们正在开发一个大型商业综合体项目，需要AI建筑设计方面的合作伙伴...',\n                requirements: [\n                    'AI设计经验',\n                    'BIM技术',\n                    '团队规模10+'\n                ],\n                matchType: '技术合作',\n                status: 'open'\n            },\n            {\n                id: 'e2',\n                title: '智慧城市项目寻求技术团队',\n                creator: '政府采购部门',\n                avatar: '🏛️',\n                budget: '200-500万',\n                duration: '6-12个月',\n                location: '上海',\n                publishTime: '4小时前',\n                tags: [\n                    '政府项目',\n                    '智慧城市',\n                    '技术团队'\n                ],\n                description: '智慧城市基础设施建设项目，需要具备AI、物联网、大数据技术的团队...',\n                requirements: [\n                    '政府项目经验',\n                    '资质齐全',\n                    '技术实力强'\n                ],\n                matchType: '工程项目匹配',\n                status: 'open'\n            }\n        ]\n    };\n    // 分类选项\n    const categories = [\n        {\n            key: 'all',\n            name: '全部',\n            icon: '🌟'\n        },\n        {\n            key: 'ai',\n            name: 'AI技术',\n            icon: '🤖'\n        },\n        {\n            key: 'architecture',\n            name: '建筑设计',\n            icon: '🏗️'\n        },\n        {\n            key: 'smart-city',\n            name: '智慧城市',\n            icon: '🏙️'\n        },\n        {\n            key: 'web3',\n            name: 'Web3',\n            icon: '🌐'\n        },\n        {\n            key: 'iot',\n            name: '物联网',\n            icon: '🔗'\n        },\n        {\n            key: 'vr',\n            name: 'VR/AR',\n            icon: '🥽'\n        }\n    ];\n    // 显示成功提示\n    const showToast = (message)=>{\n        setToastMessage(message);\n        setShowSuccessToast(true);\n        setTimeout(()=>setShowSuccessToast(false), 3000);\n    };\n    // 搜索和筛选功能\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DiscoveryPage.useEffect\": ()=>{\n            const filterContent = {\n                \"DiscoveryPage.useEffect.filterContent\": ()=>{\n                    const filtered = {};\n                    Object.keys(discoveryContent).forEach({\n                        \"DiscoveryPage.useEffect.filterContent\": (type)=>{\n                            filtered[type] = discoveryContent[type].filter({\n                                \"DiscoveryPage.useEffect.filterContent\": (item)=>{\n                                    // 搜索筛选\n                                    const matchesSearch = !searchQuery || item.title.toLowerCase().includes(searchQuery.toLowerCase()) || item.creator.toLowerCase().includes(searchQuery.toLowerCase()) || item.tags.some({\n                                        \"DiscoveryPage.useEffect.filterContent\": (tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase())\n                                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                                    // 分类筛选\n                                    const matchesCategory = selectedCategory === 'all' || item.tags.some({\n                                        \"DiscoveryPage.useEffect.filterContent\": (tag)=>{\n                                            switch(selectedCategory){\n                                                case 'ai':\n                                                    return tag.includes('AI') || tag.includes('智能');\n                                                case 'architecture':\n                                                    return tag.includes('建筑') || tag.includes('设计');\n                                                case 'smart-city':\n                                                    return tag.includes('智慧城市') || tag.includes('城市');\n                                                case 'web3':\n                                                    return tag.includes('Web3') || tag.includes('区块链') || tag.includes('NFT');\n                                                case 'iot':\n                                                    return tag.includes('物联网') || tag.includes('IoT');\n                                                case 'vr':\n                                                    return tag.includes('VR') || tag.includes('AR') || tag.includes('元宇宙');\n                                                default:\n                                                    return true;\n                                            }\n                                        }\n                                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                                    return matchesSearch && matchesCategory;\n                                }\n                            }[\"DiscoveryPage.useEffect.filterContent\"]);\n                        }\n                    }[\"DiscoveryPage.useEffect.filterContent\"]);\n                    setFilteredContent(filtered);\n                }\n            }[\"DiscoveryPage.useEffect.filterContent\"];\n            filterContent();\n        }\n    }[\"DiscoveryPage.useEffect\"], [\n        searchQuery,\n        selectedCategory\n    ]);\n    // 点赞功能\n    const handleLike = (itemId)=>{\n        setLikedItems((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(itemId)) {\n                newSet.delete(itemId);\n                showToast('取消点赞');\n            } else {\n                newSet.add(itemId);\n                showToast('点赞成功');\n            }\n            return newSet;\n        });\n    };\n    // 收藏功能\n    const handleSave = (itemId)=>{\n        setSavedItems((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(itemId)) {\n                newSet.delete(itemId);\n                showToast('取消收藏');\n            } else {\n                newSet.add(itemId);\n                showToast('收藏成功');\n            }\n            return newSet;\n        });\n    };\n    // 关注功能\n    const handleFollow = (userId)=>{\n        setFollowedUsers((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(userId)) {\n                newSet.delete(userId);\n                showToast('取消关注');\n            } else {\n                newSet.add(userId);\n                showToast('关注成功');\n            }\n            return newSet;\n        });\n    };\n    // 观看视频\n    const handleWatchVideo = (video)=>{\n        setSelectedContent(video);\n        setShowContentDetail(true);\n    };\n    // 阅读文章\n    const handleReadArticle = (article)=>{\n        setSelectedContent(article);\n        setShowContentDetail(true);\n    };\n    // 分享功能\n    const handleShare = (content)=>{\n        setSelectedContent(content);\n        setShowShareModal(true);\n    };\n    // 加入/退出群组\n    const handleJoinGroup = (groupId)=>{\n        setJoinedGroups((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(groupId)) {\n                newSet.delete(groupId);\n                showToast('已退出群组');\n            } else {\n                newSet.add(groupId);\n                showToast('成功加入群组');\n            }\n            return newSet;\n        });\n    };\n    // 查看项目详情\n    const handleViewProject = (project)=>{\n        setSelectedProject(project);\n        setShowContentDetail(true);\n    };\n    // 申请项目\n    const handleApplyProject = (project)=>{\n        setSelectedProject(project);\n        setShowApplyProject(true);\n    };\n    // NFT购买\n    const handleBuyNFT = (content)=>{\n        setSelectedContent(content);\n        setShowNFTModal(true);\n    };\n    // 创作者打赏\n    const handleTipCreator = (content)=>{\n        setSelectedContent(content);\n        setShowTipModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 bg-black/20 backdrop-blur-lg border-b border-white/10 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-purple-400 to-blue-500 rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"\\uD83D\\uDD0D\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"内容发现\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-purple-300\",\n                                                        children: \"探索 • 学习 • 连接\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400\",\n                                                children: \"●\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" 15.6k 在线\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            placeholder: \"搜索内容、创作者、标签...\",\n                                            className: \"w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 pl-10 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-white/10 rounded-lg p-1 mb-4\",\n                                children: [\n                                    {\n                                        key: 'video',\n                                        title: '视频内容',\n                                        icon: '🎬'\n                                    },\n                                    {\n                                        key: 'discovery',\n                                        title: '图文发现',\n                                        icon: '📰'\n                                    },\n                                    {\n                                        key: 'community',\n                                        title: '社群讨论',\n                                        icon: '👥'\n                                    },\n                                    {\n                                        key: 'ecosystem',\n                                        title: '生态匹配',\n                                        icon: '🤝'\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.key),\n                                        className: \"flex-1 flex flex-col items-center py-3 px-2 rounded-md text-xs font-medium transition-colors \".concat(activeTab === tab.key ? 'bg-purple-500 text-white' : 'text-white/70 hover:text-white'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg mb-1\",\n                                                children: tab.icon\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.title\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, tab.key, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 overflow-x-auto pb-2\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedCategory(category.key),\n                                        className: \"flex-shrink-0 flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors \".concat(selectedCategory === category.key ? 'bg-green-500 text-white' : 'bg-white/10 text-gray-300 hover:bg-white/20'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category.icon\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, category.key, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4 pb-20\",\n                        children: [\n                            activeTab === 'video' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.video || discoveryContent.video).map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: video.thumbnail,\n                                                        alt: video.title,\n                                                        className: \"w-full h-48 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-2 right-2 bg-black/70 px-2 py-1 rounded text-xs text-white\",\n                                                        children: video.duration\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-2 left-2 flex space-x-2\",\n                                                        children: [\n                                                            video.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-purple-500 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: \"\\uD83C\\uDFA8 NFT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            video.didVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-500 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: \"\\uD83C\\uDD94 DID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center\",\n                                                                children: video.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: video.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            video.creator,\n                                                                            \" • \",\n                                                                            video.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-400 font-bold text-sm\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            video.rewards,\n                                                                            \" NGT\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"奖励\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-sm mb-3 line-clamp-2\",\n                                                        children: video.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mb-3\",\n                                                        children: video.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-purple-500/20 rounded text-xs text-purple-300\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    tag\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDC41️ \",\n                                                                            video.views.toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleLike(video.id),\n                                                                        className: \"flex items-center space-x-1 transition-colors \".concat(likedItems.has(video.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: likedItems.has(video.id) ? '❤️' : '🤍'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 490,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: video.likes + (likedItems.has(video.id) ? 1 : 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                lineNumber: 491,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleSave(video.id),\n                                                                        className: \"transition-colors \".concat(savedItems.has(video.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'),\n                                                                        children: savedItems.has(video.id) ? '⭐' : '☆'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleShare(video),\n                                                                        className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                                        children: \"分享\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    video.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleBuyNFT(video),\n                                                                        className: \"px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors\",\n                                                                        children: \"购买NFT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleWatchVideo(video),\n                                                                        className: \"px-3 py-1 bg-purple-500 rounded-lg text-xs font-medium hover:bg-purple-600 transition-colors\",\n                                                                        children: \"观看\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, video.id, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'discovery' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.discovery || discoveryContent.discovery).map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-sm\",\n                                                                    children: article.avatar\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            article.creator,\n                                                                            \" • \",\n                                                                            article.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-400 font-bold text-sm\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            article.rewards,\n                                                                            \" NGT\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-bold text-white mb-2\",\n                                                            children: article.title\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-sm mb-3 line-clamp-3\",\n                                                            children: article.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                                            children: article.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        tag\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCD6 \",\n                                                                                article.readTime\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"\\uD83D\\uDC41️ \",\n                                                                                article.views.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleLike(article.id),\n                                                                            className: \"flex items-center space-x-1 transition-colors \".concat(likedItems.has(article.id) ? 'text-red-400' : 'text-gray-400 hover:text-red-400'),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: likedItems.has(article.id) ? '❤️' : '🤍'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 571,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: article.likes + (likedItems.has(article.id) ? 1 : 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                                    lineNumber: 572,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleSave(article.id),\n                                                                            className: \"transition-colors \".concat(savedItems.has(article.id) ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'),\n                                                                            children: savedItems.has(article.id) ? '⭐' : '☆'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleShare(article),\n                                                                            className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                                            children: \"分享\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        article.nftEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleBuyNFT(article),\n                                                                            className: \"px-3 py-1 bg-purple-500/20 border border-purple-500 rounded-lg text-xs font-medium hover:bg-purple-500/30 transition-colors\",\n                                                                            children: \"购买NFT\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleReadArticle(article),\n                                                                            className: \"px-3 py-1 bg-blue-500 rounded-lg text-xs font-medium hover:bg-blue-600 transition-colors\",\n                                                                            children: \"阅读\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this),\n                                                article.images && article.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 m-4 rounded-lg overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: article.images[0],\n                                                        alt: article.title,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, article.id, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'community' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.community || discoveryContent.community).map((community)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                children: community.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: community.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            community.creator,\n                                                                            \" • \",\n                                                                            community.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat(community.activity === 'high' ? 'bg-green-400' : community.activity === 'medium' ? 'bg-yellow-400' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: community.activity === 'high' ? '活跃' : community.activity === 'medium' ? '一般' : '较少'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm mb-3\",\n                                                children: community.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 mb-3\",\n                                                children: community.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-green-500/20 rounded text-xs text-green-300\",\n                                                        children: [\n                                                            \"#\",\n                                                            tag\n                                                        ]\n                                                    }, idx, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"\\uD83D\\uDC65 \",\n                                                                    community.members.toLocaleString(),\n                                                                    \" 成员\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"\\uD83D\\uDCAC \",\n                                                                    community.posts,\n                                                                    \" 讨论\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300\",\n                                                                children: community.groupType\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleShare(community),\n                                                                className: \"px-3 py-1 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                                children: \"分享\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleJoinGroup(community.id),\n                                                                className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(joinedGroups.has(community.id) ? 'bg-gray-600 text-gray-300 hover:bg-gray-500' : 'bg-gradient-to-r from-green-500 to-teal-500 text-white hover:from-green-600 hover:to-teal-600'),\n                                                                children: joinedGroups.has(community.id) ? '已加入' : '加入讨论'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, community.id, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'ecosystem' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: (filteredContent.ecosystem || discoveryContent.ecosystem).map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center text-xl\",\n                                                                children: project.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: project.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 703,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: [\n                                                                            project.creator,\n                                                                            \" • \",\n                                                                            project.publishTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 702,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(project.status === 'open' ? 'bg-green-500' : 'bg-gray-500'),\n                                                            children: project.status === 'open' ? '招募中' : '已结束'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                            lineNumber: 708,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm mb-3\",\n                                                children: project.description\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"预算范围\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-green-400 font-bold\",\n                                                                children: project.budget\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"项目周期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 724,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-blue-400 font-bold\",\n                                                                children: project.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"项目地点\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-purple-400 font-bold\",\n                                                                children: project.location\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"匹配类型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-orange-400 font-bold\",\n                                                                children: project.matchType\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mb-2\",\n                                                        children: \"技能要求\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: project.requirements.map((req, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-orange-500/20 rounded text-xs text-orange-300\",\n                                                                children: req\n                                                            }, idx, false, {\n                                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 mb-3\",\n                                                children: project.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-red-500/20 rounded text-xs text-red-300\",\n                                                        children: [\n                                                            \"#\",\n                                                            tag\n                                                        ]\n                                                    }, idx, true, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleShare(project),\n                                                        className: \"px-3 py-2 bg-white/10 rounded-lg text-xs font-medium hover:bg-white/20 transition-colors\",\n                                                        children: \"分享\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSave(project.id),\n                                                        className: \"px-3 py-2 rounded-lg text-xs font-medium transition-colors \".concat(savedItems.has(project.id) ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500' : 'bg-white/10 text-gray-300 hover:bg-white/20'),\n                                                        children: savedItems.has(project.id) ? '已收藏' : '收藏'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewProject(project),\n                                                        className: \"flex-1 py-2 bg-white/10 border border-white/20 rounded-lg text-sm font-medium hover:bg-white/20 transition-colors\",\n                                                        children: \"查看详情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleApplyProject(project),\n                                                        className: \"flex-1 py-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-colors\",\n                                                        children: \"立即申请\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, project.id, true, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            showSuccessToast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 797,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: toastMessage\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 796,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 795,\n                columnNumber: 9\n            }, this),\n            showContentDetail && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"内容详情\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 808,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowContentDetail(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 809,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 807,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-xl\",\n                                            children: selectedContent.avatar\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 819,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: selectedContent.title\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: selectedContent.creator\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 818,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm\",\n                                    children: selectedContent.description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 828,\n                                    columnNumber: 15\n                                }, this),\n                                selectedContent.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"时长: \",\n                                        selectedContent.duration\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 17\n                                }, this),\n                                selectedContent.readTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"阅读时间: \",\n                                        selectedContent.readTime\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 837,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: (_selectedContent_tags = selectedContent.tags) === null || _selectedContent_tags === void 0 ? void 0 : _selectedContent_tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 bg-purple-500/20 rounded text-xs text-purple-300\",\n                                            children: [\n                                                \"#\",\n                                                tag\n                                            ]\n                                        }, idx, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 844,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowContentDetail(false);\n                                                handleTipCreator(selectedContent);\n                                            },\n                                            className: \"flex-1 py-3 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl text-white font-medium hover:from-green-600 hover:to-teal-600 transition-colors\",\n                                            children: \"\\uD83D\\uDCB0 打赏创作者\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 851,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowContentDetail(false),\n                                            className: \"flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: \"关闭\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 860,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 817,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 806,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 805,\n                columnNumber: 9\n            }, this),\n            showShareModal && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"分享内容\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 877,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowShareModal(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 876,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4 mb-6\",\n                            children: [\n                                {\n                                    name: '微信',\n                                    icon: '💬',\n                                    color: 'bg-green-500'\n                                },\n                                {\n                                    name: '微博',\n                                    icon: '📱',\n                                    color: 'bg-red-500'\n                                },\n                                {\n                                    name: 'QQ',\n                                    icon: '🐧',\n                                    color: 'bg-blue-500'\n                                },\n                                {\n                                    name: '钉钉',\n                                    icon: '💼',\n                                    color: 'bg-blue-600'\n                                },\n                                {\n                                    name: '复制链接',\n                                    icon: '🔗',\n                                    color: 'bg-gray-500'\n                                },\n                                {\n                                    name: '更多',\n                                    icon: '⋯',\n                                    color: 'bg-purple-500'\n                                }\n                            ].map((platform)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        showToast(\"已分享到\".concat(platform.name));\n                                        setShowShareModal(false);\n                                    },\n                                    className: \"\".concat(platform.color, \" rounded-xl p-4 text-white text-center hover:opacity-80 transition-opacity\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-1\",\n                                            children: platform.icon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs\",\n                                            children: platform.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 904,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, platform.name, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 895,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 886,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowShareModal(false),\n                            className: \"w-full py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 909,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 875,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 874,\n                columnNumber: 9\n            }, this),\n            showApplyProject && selectedProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20 max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"申请项目\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowApplyProject(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-white mb-2\",\n                                            children: selectedProject.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 935,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: selectedProject.description\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 936,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 934,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"预算范围\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 941,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400 font-bold\",\n                                                    children: selectedProject.budget\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 942,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"项目周期\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 945,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400 font-bold\",\n                                                    children: selectedProject.duration\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 946,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"个人简介\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\",\n                                            rows: 3,\n                                            placeholder: \"请简要介绍您的相关经验和技能...\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 952,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 950,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"联系方式\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 960,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\",\n                                            placeholder: \"请输入您的联系方式...\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 961,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-white mb-2\",\n                                            children: \"期望报酬\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\",\n                                            placeholder: \"请输入您的期望报酬...\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 970,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 968,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowApplyProject(false),\n                                            className: \"flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 978,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowApplyProject(false);\n                                                showToast('申请已提交，请等待回复');\n                                            },\n                                            className: \"flex-1 py-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl text-white font-medium hover:from-orange-600 hover:to-red-600 transition-colors\",\n                                            children: \"提交申请\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 977,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 933,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 922,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 921,\n                columnNumber: 9\n            }, this),\n            showNFTModal && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-md w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"购买NFT\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowNFTModal(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1005,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1003,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-3xl mx-auto mb-3\",\n                                            children: \"\\uD83C\\uDFA8\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1015,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-white\",\n                                            children: selectedContent.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1018,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: [\n                                                \"by \",\n                                                selectedContent.creator\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1019,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1014,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"NFT价格\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400 font-bold\",\n                                                    children: \"0.1 ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Gas费用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-400\",\n                                                    children: \"0.005 ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                    lineNumber: 1029,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-white/10 pt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: \"总计\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400 font-bold\",\n                                                        children: \"0.105 ETH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                                lineNumber: 1032,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1022,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"购买后您将获得该内容的NFT所有权证明，可在二级市场交易。\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1039,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowNFTModal(false),\n                                            className: \"flex-1 py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowNFTModal(false);\n                                                showToast('NFT购买成功！');\n                                            },\n                                            className: \"flex-1 py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl text-white font-medium hover:from-purple-600 hover:to-pink-600 transition-colors\",\n                                            children: \"确认购买\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1050,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1013,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 1002,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 1001,\n                columnNumber: 9\n            }, this),\n            showTipModal && selectedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/80 backdrop-blur-lg flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-gray-900 to-black rounded-3xl p-6 max-w-sm w-full border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"打赏创作者\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowTipModal(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1071,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1069,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-2xl mx-auto mb-2\",\n                                            children: selectedContent.avatar\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1081,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: selectedContent.creator\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1084,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"感谢优质内容创作\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1085,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1080,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-3\",\n                                    children: [\n                                        10,\n                                        50,\n                                        100,\n                                        200,\n                                        500,\n                                        1000\n                                    ].map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowTipModal(false);\n                                                showToast(\"成功打赏 \".concat(amount, \" NGT\"));\n                                            },\n                                            className: \"py-3 bg-white/10 border border-white/20 rounded-lg text-white font-medium hover:bg-white/20 transition-colors\",\n                                            children: [\n                                                amount,\n                                                \" NGT\"\n                                            ]\n                                        }, amount, true, {\n                                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                            lineNumber: 1090,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1088,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        placeholder: \"自定义金额\",\n                                        className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                        lineNumber: 1104,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1103,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowTipModal(false),\n                                    className: \"w-full py-3 bg-white/10 border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-colors\",\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                                    lineNumber: 1111,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                            lineNumber: 1079,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                    lineNumber: 1068,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n                lineNumber: 1067,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\2025\\\\20250710\\\\zhumengyun-frontend\\\\mobile\\\\src\\\\app\\\\engineering\\\\page.tsx\",\n        lineNumber: 349,\n        columnNumber: 5\n    }, this);\n}\n_s(DiscoveryPage, \"p5BLHYhD7bS/EXdk2xWA/9sGK9M=\");\n_c = DiscoveryPage;\nvar _c;\n$RefreshReg$(_c, \"DiscoveryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/engineering/page.tsx\n"));

/***/ })

});