'use client'

import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'

export default function WeChatSocialPage() {
  const [activeTab, setActiveTab] = useState<'chats' | 'contacts' | 'discover'>('chats')
  const [selectedChat, setSelectedChat] = useState<string | null>(null)
  const [newMessage, setNewMessage] = useState('')
  const [showProfile, setShowProfile] = useState(false)
  const [showGroupInfo, setShowGroupInfo] = useState(false)

  // 聊天列表数据
  const chatList = [
    {
      id: 'ai-architects',
      name: 'AI建筑师联盟',
      avatar: '🏗️',
      lastMessage: '张工程师: 刚完成了智慧城市项目的VR展示',
      time: '刚刚',
      unread: 3,
      type: 'group',
      members: 128,
      isOnline: true
    },
    {
      id: 'nextgen-team',
      name: 'NextGen 2025核心团队',
      avatar: '🚀',
      lastMessage: '李产品: 新版本的AI功能已经上线了',
      time: '2分钟前',
      unread: 1,
      type: 'group',
      members: 15,
      isOnline: true
    },
    {
      id: 'vr-designers',
      name: 'VR设计师交流群',
      avatar: '🌌',
      lastMessage: '王设计师: 分享一个元宇宙展厅的设计案例',
      time: '5分钟前',
      unread: 0,
      type: 'group',
      members: 89,
      isOnline: true
    },
    {
      id: 'ai-expert',
      name: 'AI技术专家',
      avatar: '🤖',
      lastMessage: '最新的GPT模型在工程设计中表现如何？',
      time: '10分钟前',
      unread: 0,
      type: 'private',
      isOnline: true
    },
    {
      id: 'blockchain-dev',
      name: '区块链开发者',
      avatar: '⛓️',
      lastMessage: '智能合约的gas费优化方案',
      time: '30分钟前',
      unread: 0,
      type: 'private',
      isOnline: false
    },
    {
      id: 'project-manager',
      name: '项目经理小刘',
      avatar: '📊',
      lastMessage: '明天的项目评审会议记得参加',
      time: '1小时前',
      unread: 0,
      type: 'private',
      isOnline: true
    }
  ]

  // 联系人数据
  const contacts = [
    {
      id: 'ai-architects-group',
      name: 'AI建筑师联盟',
      avatar: '🏗️',
      type: 'group',
      members: 128,
      description: '专业建筑师AI技术交流'
    },
    {
      id: 'nextgen-team-group',
      name: 'NextGen 2025核心团队',
      avatar: '🚀',
      type: 'group',
      members: 15,
      description: '平台核心开发团队'
    },
    {
      id: 'vr-designers-group',
      name: 'VR设计师交流群',
      avatar: '🌌',
      type: 'group',
      members: 89,
      description: '虚拟现实设计专业交流'
    }
  ]

  // 发现页面数据
  const discoverItems = [
    {
      id: 'moments',
      title: '朋友圈',
      icon: '📷',
      description: '查看朋友的最新动态',
      badge: 5
    },
    {
      id: 'channels',
      title: '视频号',
      icon: '🎬',
      description: '发现精彩短视频内容',
      badge: 0
    },
    {
      id: 'miniprogram',
      title: '小程序',
      icon: '⚡',
      description: 'NextGen 2025工具集',
      badge: 0
    },
    {
      id: 'nft-market',
      title: 'NFT市场',
      icon: '🎨',
      description: '数字艺术品交易平台',
      badge: 2
    },
    {
      id: 'dao-governance',
      title: 'DAO治理',
      icon: '🏛️',
      description: '参与平台治理决策',
      badge: 1
    },
    {
      id: 'ai-tools',
      title: 'AI工具箱',
      icon: '🤖',
      description: '智能设计辅助工具',
      badge: 0
    }
  ]

  // 当前聊天的消息数据
  const currentMessages = [
    {
      id: 1,
      sender: '张工程师',
      avatar: '👨‍💼',
      content: '大家好，我刚完成了一个智慧城市项目的VR展示，效果非常震撼！',
      time: '14:30',
      type: 'text',
      isMe: false
    },
    {
      id: 2,
      sender: '我',
      avatar: '👤',
      content: '太棒了！可以分享一下技术细节吗？',
      time: '14:32',
      type: 'text',
      isMe: true
    },
    {
      id: 3,
      sender: 'AI专家',
      avatar: '🤖',
      content: '我们使用了最新的AI算法来优化建筑布局，结合VR技术实现了沉浸式体验',
      time: '14:35',
      type: 'text',
      isMe: false
    },
    {
      id: 4,
      sender: '李设计师',
      avatar: '🎨',
      content: '这个项目的视觉效果确实很棒，我也想学习一下相关技术',
      time: '14:38',
      type: 'text',
      isMe: false
    }
  ]

  const handleSendMessage = useCallback(() => {
    if (newMessage.trim()) {
      // 这里可以添加发送消息的逻辑
      setNewMessage('')
    }
  }, [newMessage])

  const formatTime = (time: string) => {
    return time
  }

  // 如果选中了聊天，显示聊天界面
  if (selectedChat) {
    const chat = chatList.find(c => c.id === selectedChat)
    return (
      <div className="h-screen bg-gray-50 flex flex-col">
        {/* 聊天头部 */}
        <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button 
              onClick={() => setSelectedChat(null)}
              className="text-blue-500 text-lg"
            >
              ←
            </button>
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
              <span className="text-white text-lg">{chat?.avatar}</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{chat?.name}</h3>
              {chat?.type === 'group' && (
                <p className="text-sm text-gray-500">{chat.members}人</p>
              )}
            </div>
          </div>
          <button 
            onClick={() => setShowGroupInfo(true)}
            className="text-gray-500"
          >
            ⋯
          </button>
        </div>

        {/* 消息列表 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {currentMessages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.isMe ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`flex items-end space-x-2 max-w-xs ${message.isMe ? 'flex-row-reverse space-x-reverse' : ''}`}>
                {!message.isMe && (
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm">{message.avatar}</span>
                  </div>
                )}
                <div className="flex flex-col">
                  {!message.isMe && (
                    <span className="text-xs text-gray-500 mb-1 px-2">{message.sender}</span>
                  )}
                  <div className={`px-4 py-2 rounded-2xl ${
                    message.isMe 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-white text-gray-900 border border-gray-200'
                  }`}>
                    <p className="text-sm">{message.content}</p>
                  </div>
                  <span className="text-xs text-gray-400 mt-1 px-2">{message.time}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 消息输入 */}
        <div className="bg-white border-t border-gray-200 p-4">
          <div className="flex items-center space-x-3">
            <button className="text-gray-500 text-xl">🎤</button>
            <div className="flex-1 flex items-center bg-gray-100 rounded-full px-4 py-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="输入消息..."
                className="flex-1 bg-transparent outline-none text-gray-900 placeholder-gray-500"
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              />
              <button className="text-gray-500 text-lg ml-2">😊</button>
            </div>
            <button 
              onClick={handleSendMessage}
              className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center"
            >
              ➤
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="flex items-center justify-between px-4 py-3">
          <h1 className="text-lg font-semibold text-gray-900">微信</h1>
          <div className="flex items-center space-x-4">
            <button className="text-gray-600 text-xl">🔍</button>
            <button className="text-gray-600 text-xl">➕</button>
          </div>
        </div>
        
        {/* 标签栏 */}
        <div className="flex">
          {[
            { key: 'chats', label: '聊天', count: chatList.filter(c => c.unread > 0).length },
            { key: 'contacts', label: '通讯录', count: 0 },
            { key: 'discover', label: '发现', count: 8 }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex-1 py-3 text-center relative ${
                activeTab === tab.key 
                  ? 'text-green-600 border-b-2 border-green-600' 
                  : 'text-gray-600'
              }`}
            >
              <span className="font-medium">{tab.label}</span>
              {tab.count > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'chats' && (
          <div className="divide-y divide-gray-100">
            {chatList.map((chat) => (
              <motion.div
                key={chat.id}
                whileTap={{ scale: 0.98 }}
                onClick={() => setSelectedChat(chat.id)}
                className="bg-white px-4 py-3 flex items-center space-x-3 cursor-pointer hover:bg-gray-50"
              >
                <div className="relative">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                    <span className="text-white text-lg">{chat.avatar}</span>
                  </div>
                  {chat.isOnline && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-medium text-gray-900 truncate">{chat.name}</h3>
                    <span className="text-xs text-gray-500">{chat.time}</span>
                  </div>
                  <p className="text-sm text-gray-600 truncate">{chat.lastMessage}</p>
                </div>
                {chat.unread > 0 && (
                  <div className="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {chat.unread}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}

        {activeTab === 'contacts' && (
          <div className="p-4 space-y-4">
            <div className="bg-white rounded-xl p-4">
              <h3 className="font-semibold text-gray-900 mb-3">群聊</h3>
              <div className="space-y-3">
                {contacts.map((contact) => (
                  <div key={contact.id} className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                      <span className="text-white">{contact.avatar}</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{contact.name}</h4>
                      <p className="text-sm text-gray-500">{contact.description}</p>
                    </div>
                    <span className="text-xs text-gray-400">{contact.members}人</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'discover' && (
          <div className="p-4 space-y-4">
            {discoverItems.map((item) => (
              <motion.div
                key={item.id}
                whileTap={{ scale: 0.98 }}
                className="bg-white rounded-xl p-4 flex items-center space-x-4 cursor-pointer"
              >
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                  <span className="text-white text-xl">{item.icon}</span>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{item.title}</h3>
                  <p className="text-sm text-gray-500">{item.description}</p>
                </div>
                {item.badge > 0 && (
                  <div className="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {item.badge}
                  </div>
                )}
                <span className="text-gray-400">›</span>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
